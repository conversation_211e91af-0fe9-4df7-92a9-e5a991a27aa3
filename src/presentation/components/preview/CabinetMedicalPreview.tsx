import React, { useEffect, useRef } from "react";
import {
  X,
  MapPin,
  Phone,
  Mail,
  GraduationCap,
  Briefcase,
  FileText,
  Globe,
  Tag,
} from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useCabinetMedicalPreview } from "@/presentation/hooks/preview";
import { professionnels_types_consultation_enum } from "@/domain/models/enums";

const CabinetMedicalPreview: React.FC = () => {
  const {
    previewOpen: open,
    closePreview: onClose,
    previewData: data,
  } = useCabinetMedicalPreview();

  const modalRef = useRef<HTMLDivElement>(null);

  // Fermer la modal quand on clique en dehors
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (open) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [open, onClose]);

  // Empêcher le scroll du body quand la modal est ouverte
  useEffect(() => {
    if (open) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [open]);

  const {
    titre,
    nom,
    prenom,
    specialite,
    specialities,
    raisonSociale,
    adresse,
    region,
    district,
    commune,
    fokotany,
    infoAcces,
    email,
    telephone,
    presentation,
    profileImage,
    cabinetImages,
    typeConsultation,
    nouveauPatientAcceptes,
    paymentMethods,
    insurances,
    motCles,
    diplomes,
    experiences,
    publications,
    langues,
  } = data;

  // Formatage de l'adresse complète
  const fullAddress = [
    adresse,
    fokotany,
    commune?.nom,
    district?.libelle,
    region?.nom,
  ]
    .filter(Boolean)
    .join(", ");

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div
        ref={modalRef}
        className="relative bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-auto p-4 sm:p-6 md:p-8"
      >
        {/* En-tête */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Prévisualisation du profil</h2>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100"
            aria-label="Fermer"
          >
            <X size={20} />
          </button>
        </div>

        <p className="text-gray-500 italic mb-6">
          Voici comment votre cabinet médical apparaîtra aux patients sur MEDDoC
        </p>

        <hr className="border-gray-200 mb-6" />

        {/* Section profil */}
        <div className="flex flex-col sm:flex-row mb-8">
          <div className="flex flex-col items-center sm:mr-6 mb-4 sm:mb-0">
            <img
              src={profileImage || "/placeholder-avatar.png"}
              alt="Photo de profil"
              className="w-32 h-32 sm:w-36 sm:h-36 md:w-40 md:h-40 rounded-full object-cover border-4 border-gray-100 mb-2"
            />
            {specialite && (
              <span className="mt-2 px-3 py-1 bg-meddoc-primary text-white text-sm rounded-full">
                {specialite}
              </span>
            )}
            {specialities && specialities.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1 justify-center">
                {specialities.map((spec) => (
                  <span
                    key={spec.id}
                    className="px-2 py-0.5 bg-meddoc-primary text-white text-xs rounded-full"
                  >
                    {spec.nom_specialite}
                  </span>
                ))}
              </div>
            )}
          </div>

          <div className="flex-1">
            <h1 className="text-2xl font-bold mb-1">
              {titre} {prenom} {nom}
            </h1>
            <p className="text-lg text-gray-600 mb-4">{raisonSociale}</p>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
              <div className="flex">
                <MapPin
                  size={18}
                  className="text-meddoc-primary mr-2 mt-1 flex-shrink-0"
                />
                <div>
                  <p className="font-semibold text-sm">Adresse</p>
                  <p className="text-sm">{fullAddress}</p>
                  {infoAcces && (
                    <p className="text-sm text-gray-500 mt-1">{infoAcces}</p>
                  )}
                </div>
              </div>

              <div className="flex">
                <Phone
                  size={18}
                  className="text-meddoc-primary mr-2 mt-1 flex-shrink-0"
                />
                <div>
                  <p className="font-semibold text-sm">Contact</p>
                  <p className="text-sm">{telephone}</p>
                  <p className="text-sm">{email}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Section À propos */}
        {presentation && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-2">À propos</h3>
            <p className="text-base">{presentation}</p>
          </div>
        )}

        {/* Section Photos */}
        {cabinetImages && cabinetImages.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-3">Photos du cabinet</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {cabinetImages.map((image, index) => (
                <div key={index} className="h-48 rounded-lg overflow-hidden">
                  <img
                    src={image}
                    alt={`Cabinet médical ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Section Services */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-3">Services proposés</h3>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {typeConsultation && (
              <div>
                <p className="font-semibold text-sm">Type de consultation</p>
                <p className="text-sm">
                  {typeConsultation &&
                  typeConsultation.includes(
                    professionnels_types_consultation_enum["EN_CABINET"]
                  )
                    ? "En cabinet uniquement"
                    : "À domicile uniquement"}
                </p>
              </div>
            )}

            <div>
              <p className="font-semibold text-sm">Nouveaux patients</p>
              <p className="text-sm">
                {nouveauPatientAcceptes
                  ? "Accepte de nouveaux patients"
                  : "N'accepte pas de nouveaux patients"}
              </p>
            </div>

            {paymentMethods && paymentMethods.length > 0 && (
              <div className="sm:col-span-2">
                <p className="font-semibold text-sm mb-2">
                  Modes de paiement acceptés
                </p>
                <div className="flex flex-wrap gap-1">
                  {paymentMethods.map((method) => (
                    <span
                      key={method.id}
                      className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full"
                    >
                      {method.name}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {insurances && insurances.length > 0 && (
              <div className="sm:col-span-2">
                <p className="font-semibold text-sm mb-2">
                  Assurances acceptées
                </p>
                <div className="flex flex-wrap gap-1">
                  {insurances.map((insurance) => (
                    <span
                      key={insurance.id}
                      className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full"
                    >
                      {insurance.nom}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Section Mots-clés */}
        {motCles && motCles.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-3 flex items-center">
              <Tag size={18} className="mr-2" />
              Mots-clés
            </h3>
            <div className="flex flex-wrap gap-1">
              {motCles.map((motCle) => (
                <span
                  key={motCle.id}
                  className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full"
                >
                  {motCle.symptome}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Section Diplômes */}
        {diplomes && diplomes.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-3 flex items-center">
              <GraduationCap size={18} className="mr-2" />
              Formation et diplômes
            </h3>
            <div className="space-y-4">
              {diplomes.map((diplome, index) => (
                <div key={index} className="mb-2">
                  <p className="font-semibold text-sm">{diplome.titre}</p>
                  <p className="text-sm">
                    {diplome.etablissement} - {diplome.annee}
                  </p>
                  {diplome.description && (
                    <p className="text-sm text-gray-500 mt-1">
                      {diplome.description}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Section Expériences */}
        {experiences && experiences.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-3 flex items-center">
              <Briefcase size={18} className="mr-2" />
              Expérience professionnelle
            </h3>
            <div className="space-y-4">
              {experiences.map((experience, index) => (
                <div key={index} className="mb-2">
                  <p className="font-semibold text-sm">{experience.poste}</p>
                  <p className="text-sm">
                    {experience.etablissement} - {experience.date_debut}
                    {experience.est_actuel
                      ? " à aujourd'hui"
                      : experience.date_fin
                        ? ` à ${experience.date_fin}`
                        : ""}
                  </p>
                  {experience.description && (
                    <p className="text-sm text-gray-500 mt-1">
                      {experience.description}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Section Publications */}
        {publications && publications.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-3 flex items-center">
              <FileText size={18} className="mr-2" />
              Publications
            </h3>
            <div className="space-y-4">
              {publications.map((publication, index) => (
                <div key={index} className="mb-2">
                  <p className="font-semibold text-sm">
                    {publication.titre} ({publication.annee})
                  </p>
                  {publication.description && (
                    <p className="text-sm text-gray-500 mt-1">
                      {publication.description}
                    </p>
                  )}
                  {publication.lien && (
                    <a
                      href={publication.lien}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-meddoc-primary hover:underline block mt-1"
                    >
                      Voir la publication
                    </a>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Section Langues */}
        {langues && langues.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-3 flex items-center">
              <Globe size={18} className="mr-2" />
              Langues parlées
            </h3>
            <div className="flex flex-wrap gap-1">
              {langues.map((langue, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full"
                >
                  {langue.nom_langue}
                </span>
              ))}
            </div>
          </div>
        )}



        {/* Bouton de fermeture */}
        <div className="flex justify-center mt-4">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-meddoc-primary text-meddoc-primary rounded-md hover:bg-meddoc-primary hover:text-white transition-colors min-w-[200px]"
          >
            Fermer la prévisualisation
          </button>
        </div>
      </div>
    </div>
  );
};

export default CabinetMedicalPreview;
