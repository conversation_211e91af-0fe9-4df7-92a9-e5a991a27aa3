import { memo, useMemo } from "react";
import { Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import {
  User,
  CheckCircle,
  Circle,
  ChevronDown,
  FileText,
  Building,
  FileDigit,
  Landmark,
} from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useProfessionalInfoSection } from "@/presentation/hooks/sections";
import { professionnels_titre_enum, sexe_enum } from "@/domain/models/enums";
import { useFormContext } from "react-hook-form";
import { FormField, OrdreAppartenanceSelect } from "./components";
import { useSectionProgress } from "@/presentation/hooks/sections/use-section-progress";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

const ProfessionalInfoSection = () => {
  const {
    isExpanded,
    handleAccordionChange,
    ordresList,
    selectedOrd<PERSON>,
    handleOrdreAppartenanceChange,
  } = useProfessionalInfoSection();

  const {
    formState: { errors },
    watch,
    register,
  } = useFormContext<CabinetMedicalFormDTO>();

  // Surveiller uniquement les champs nécessaires pour la progression de la section
  const watchedValues = watch([
    "titre",
    "nom",
    "prenom",
    "sexe",
    "numero_ordre",
    "raison_sociale",
    "nif",
    "stat",
    "ordre_appartenance",
  ]);

  const [
    titre,
    nom,
    prenom,
    sexe,
    numero_ordre,
    raison_sociale,
    nif,
    stat,
    ordre_appartenance,
  ] = watchedValues;

  // Calculer la progression de la section avec useMemo pour éviter des recalculs inutiles
  const sectionComplete = useMemo(
    () =>
      !!titre &&
      !!nom &&
      !!prenom &&
      !!sexe &&
      !!numero_ordre &&
      !!raison_sociale &&
      !!nif &&
      !!stat &&
      ordre_appartenance?.length > 0,
    [
      titre,
      nom,
      prenom,
      sexe,
      numero_ordre,
      raison_sociale,
      nif,
      stat,
      ordre_appartenance,
    ]
  );

  // Utiliser le hook pour gérer la progression de la section
  const { isComplete } = useSectionProgress("panel1", sectionComplete);

  return (
    <Accordion
      expanded={isExpanded}
      onChange={handleAccordionChange}
      sx={{ width: "100%", mb: 1 }}
    >
      <AccordionSummary
        expandIcon={<ChevronDown />}
        aria-controls="panel1-content"
        id="panel1-header"
        sx={{
          backgroundColor: PRIMARY,
          color: "white",
          "& .MuiAccordionSummary-expandIconWrapper": {
            color: "white",
          },
        }}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <User size={20} className="mr-2" />
            <h3 className="text-base font-medium">
              Informations professionnelles
            </h3>
          </div>
          <div className="flex items-center">
            {isComplete ? (
              <CheckCircle size={20} className="mr-2" />
            ) : (
              <Circle size={20} className="mr-2 opacity-50" />
            )}
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails>
        <div className="p-2">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 md:gap-4 w-full">
            <FormField
              id="titre"
              label="Titre"
              placeholder="Sélectionnez un titre"
              type="select"
              icon={User}
              register={register}
              required
              error={errors.titre}
              validation={{
                required: "Le titre est requis",
              }}
              options={[
                { value: "", label: "Sélectionnez un titre" },
                { value: professionnels_titre_enum.PR, label: "Professeur" },
                { value: professionnels_titre_enum.DR, label: "Docteur" },
              ]}
            />
            <FormField
              id="nom"
              label="Nom"
              placeholder="Entrez votre nom"
              icon={User}
              register={register}
              required
              error={errors.nom}
              validation={{
                required: "Le nom est requis",
                minLength: {
                  value: 2,
                  message: "Le nom doit contenir au moins 2 caractères",
                },
              }}
              className="col-span-1 sm:col-span-1"
            />
            <FormField
              id="prenom"
              label="Prénom"
              placeholder="Entrez votre prénom"
              icon={User}
              register={register}
              required
              error={errors.prenom}
              validation={{
                required: "Le prénom est requis",
                minLength: {
                  value: 2,
                  message: "Le prénom doit contenir au moins 2 caractères",
                },
              }}
            />
            <FormField
              id="sexe"
              label="Sexe"
              placeholder="Sélectionnez votre sexe"
              type="select"
              icon={User}
              register={register}
              required
              error={errors.sexe}
              validation={{
                required: "Le sexe est requis",
              }}
              options={[
                { value: "", label: "Sélectionnez votre sexe" },
                { value: sexe_enum.homme, label: "Homme" },
                { value: sexe_enum.femme, label: "Femme" },
              ]}
              className="col-span-1 sm:col-span-1"
            />
            <FormField
              id="numero_ordre"
              label="Numéro d'ordre"
              placeholder="Entrez votre numéro d'ordre"
              icon={FileDigit}
              register={register}
              required
              error={errors.numero_ordre}
              validation={{
                required: "Le numéro d'ordre est requis",
                pattern: {
                  value: /^[0-9]+$/,
                  message:
                    "Le numéro d'ordre doit contenir uniquement des chiffres",
                },
              }}
              inputMode="numeric"
              pattern="[0-9]*"
            />
            <FormField
              id="raison_sociale"
              label="Raison sociale"
              placeholder="Entrez votre raison sociale"
              icon={Building}
              register={register}
              required
              error={errors.raison_sociale}
              validation={{
                required: "La raison sociale est requise",
              }}
              className="col-span-1 sm:col-span-1"
            />
            <FormField
              id="nif"
              label="NIF"
              placeholder="Entrez votre NIF"
              icon={Landmark}
              register={register}
              required
              error={errors.nif}
              validation={{
                required: "Le NIF est requis",
              }}
            />
            <FormField
              id="stat"
              label="STAT"
              placeholder="Entrez votre STAT"
              icon={FileDigit}
              register={register}
              required
              error={errors.stat}
              validation={{
                required: "Le STAT est requis",
              }}
              className="col-span-1 sm:col-span-1"
            />

            <div className="col-span-1 sm:col-span-2 mt-4">
              <div className="flex flex-col w-full">
                <label
                  htmlFor="ordreAppartenances"
                  className="flex items-center text-sm text-gray-700 mb-2"
                >
                  <FileText size={16} className="text-meddoc-primary mr-2" />
                  Ordres d'appartenance*
                </label>

                <OrdreAppartenanceSelect
                  ordresList={ordresList}
                  selectedOrdres={selectedOrdres}
                  handleOrdreAppartenanceChange={handleOrdreAppartenanceChange}
                  register={register}
                />
              </div>
            </div>
          </div>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default memo(ProfessionalInfoSection);
