import { IUpdateProfessionalPublicationRepository } from "@/domain/interfaces/repositories/professionalPublication";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_PUBLICATION_TABLE_NAME } from "./constants";

export class UpdateProfessionalPublicationRepository implements IUpdateProfessionalPublicationRepository {
  async execute(id: number, publication: Partial<PublicationProfessionnel>): Promise<PublicationProfessionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_PUBLICATION_TABLE_NAME)
      .update(publication)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return data as PublicationProfessionnel;
  }
}
