import { IGetProfessionalPublicationsByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalPublication";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_PUBLICATION_TABLE_NAME } from "./constants";

export class GetProfessionalPublicationsByProfessionalIdRepository implements IGetProfessionalPublicationsByProfessionalIdRepository {
  async execute(professionalId: number): Promise<PublicationProfessionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_PUBLICATION_TABLE_NAME)
      .select("*")
      .eq("id_professionnel", professionalId)
      .order("date_publication", { ascending: false });

    if (error) throw error;

    if (!data || data.length === 0) return [];

    return data as PublicationProfessionnel[];
  }
}
