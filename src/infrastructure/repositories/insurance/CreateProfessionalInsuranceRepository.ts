import { ICreateProfessionalInsuranceRepository } from "@/domain/interfaces/repositories/insurance";
import { AssuranceProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_INSURANCE_TABLE_NAME } from "./constants";

export class CreateProfessionalInsuranceRepository implements ICreateProfessionalInsuranceRepository {
  async execute(insurance: Omit<AssuranceProfessionnel, "id">[]): Promise<AssuranceProfessionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_INSURANCE_TABLE_NAME)
      .insert(insurance)
      .select()

    if (error) throw error;

    return data as AssuranceProfessionnel[];
  }
}
