import { IUpdateProfessionalInsuranceRepository } from "@/domain/interfaces/repositories/insurance";
import { AssuranceProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_INSURANCE_TABLE_NAME } from "./constants";

export class UpdateProfessionalInsuranceRepository implements IUpdateProfessionalInsuranceRepository {
  async execute(id: number, insurance: Partial<AssuranceProfessionnel>): Promise<AssuranceProfessionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_INSURANCE_TABLE_NAME)
      .update(insurance)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return data as AssuranceProfessionnel;
  }
}