import { IDeleteProfessionalInsuranceRepository } from "@/domain/interfaces/repositories/insurance";
import { AssuranceProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_INSURANCE_TABLE_NAME } from "./constants";

export class DeleteProfessionalInsuranceRepository
  implements IDeleteProfessionalInsuranceRepository {
  async execute(id: number): Promise<AssuranceProfessionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_INSURANCE_TABLE_NAME)
      .delete()
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return data as AssuranceProfessionnel;
  }
}
