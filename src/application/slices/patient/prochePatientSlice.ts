import { Proche } from "@/domain/models";
import { CreateProchePatientUsecase } from "@/domain/usecases/prochePatient";
import { GetProchePatientUsecase } from "@/domain/usecases/prochePatient/GetProchePatientUsecase";
import { CreateProchePatientRepository } from "@/infrastructure/repositories/prochePatient";
import { GetProchePatientRepository } from "@/infrastructure/repositories/prochePatient/GetProchePatientRepository";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { addNotification } from "../notification/notificationSlice";
import { ProcheSuccessMessage } from "@/domain/usecases/prochePatient/procheMessages/ProcheSuccessMessage";
import { ProcheErrorMessages } from "@/domain/usecases/prochePatient/procheMessages/ProcheErrorMessages";
import { DeleteProcheRepository } from "@/infrastructure/repositories/prochePatient/DeleteProcheRepository";
import { DeleteProcheUsecase } from "@/domain/usecases/prochePatient/DeleteProchePatientUsecase";

interface prochePatientSlice {
  loading: boolean;
  error: string | null;
  proches: Proche[] | null;
  isModalOpen: boolean
}

const initialState: prochePatientSlice = {
  error: "",
  loading: false,
  proches: null,
  isModalOpen: false,
};

export const createProche = createAsyncThunk(
  "prochePatient/create",
  async (procheData: Omit<Proche, "id">, { rejectWithValue, dispatch }) => {
    try {
      const createProchePatientRepository = new CreateProchePatientRepository();
      const createProchePatientUsecase = new CreateProchePatientUsecase(
        createProchePatientRepository
      );
      const data = await createProchePatientUsecase.execute(procheData);
      dispatch(addNotification({ message: ProcheSuccessMessage.PROCHE_CREATED, type: 'success' }))
      return data;
    } catch (error) {
      dispatch(addNotification({ message: ProcheErrorMessages.PROCHE_CREATED, type: 'error' }))
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

export const getProcheByPatientId = createAsyncThunk(
  "prochePatient/getProcheByPatientId",
  async (patientId: number, { rejectWithValue, dispatch }) => {
    try {
      const getProchePatientRepository = new GetProchePatientRepository();
      const getProchePatientUsecase = new GetProchePatientUsecase(
        getProchePatientRepository
      );
      const data =
        await getProchePatientUsecase.execute(patientId);
      return data;
    } catch (error) {
      dispatch(addNotification({ message: ProcheErrorMessages.PROCHE_CREATED, type: 'error' }))
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

export const deleteProcheByPatientId = createAsyncThunk(
  "prochePatient/delete",
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      const deleteProcheRepository = new DeleteProcheRepository();
      const deleteProcheUsecase = new DeleteProcheUsecase(
        deleteProcheRepository
      );
      const data =
        await deleteProcheUsecase.execute(id);
        dispatch(addNotification({ message: ProcheSuccessMessage.PROCHE_DELETED, type: 'success' }))
      return data;
    } catch (error) {
      dispatch(addNotification({ message: ProcheErrorMessages.PROCHE_DELETED, type: 'error' }))
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

const prochePatientSlice = createSlice({
  name: "prochePatient",
  initialState,
  reducers: {
    setIsPageValid: (state, action: PayloadAction<boolean>) => {
      state.isModalOpen = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createProche.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createProche.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(createProche.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getProcheByPatientId.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getProcheByPatientId.fulfilled, (state, action) => {
          state.loading = false
          state.proches = action.payload
      })
      .addCase(getProcheByPatientId.rejected, (state, action) => {
          state.loading = false
          state.error = action.payload as string
      })
      .addCase(deleteProcheByPatientId.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteProcheByPatientId.fulfilled, (state, action) => {
          state.loading = false
          state.error = null
          state.proches =  state.proches?.filter((proche) => proche.id !== action.meta.arg)
      })
      .addCase(deleteProcheByPatientId.rejected, (state, action) => {
          state.loading = false
          state.error = action.payload as string
      })
  }
});

export const {
  setIsPageValid,
} = prochePatientSlice.actions;

export default prochePatientSlice.reducer;
