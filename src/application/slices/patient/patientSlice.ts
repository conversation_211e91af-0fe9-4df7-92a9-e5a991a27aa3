import { Patient } from '@/domain/models'
import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit'
import CreatePatientRepository from '@/infrastructure/repositories/patients/CreatePatientRepository'
import GetPatientsRepository from '@/infrastructure/repositories/patients/GetPatientsRepository'
import EditPatientRepository from '@/infrastructure/repositories/patients/EditPatientRepository'
import DeletePatientRepository from '@/infrastructure/repositories/patients/DeletePatientRepository'
import { ErrorMessages } from '@/shared/constants/ErrorMessages'

interface PatientState {
  patients: Patient[] | null
  loading: boolean
  error: string | null
  selectedPatient: Patient | null
  isAddPatientOpen: boolean
  isEditPatientOpen: boolean
  isRemovePatientOpen: boolean
}

const initialState: PatientState = {
  patients: [],
  loading: false,
  error: null,
  selectedPatient: null,
  isAddPatientOpen: false,
  isEditPatientOpen: false,
  isRemovePatientOpen: false
}

export const getPatient = createAsyncThunk(
  'patient/getPatients',
  async (_, { rejectWithValue }) => {
    try {
      const getPatientsRepository = new GetPatientsRepository()
      const patients = await getPatientsRepository.execute()
      return patients
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR)
    }
  }
)

/**
 * @returns Le patient ajouté
 */
export const addPatients = createAsyncThunk(
  'patient/addpatient',
  async (patientInfo: Omit<Patient, 'id'>, { rejectWithValue }) => {
    try {

      const createPatientRepository = new CreatePatientRepository()
      const response = await createPatientRepository.execute(patientInfo)
      return response
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR)
    }
  }
)

/**
 * @param id - L'ID du patient à mettre à jour
 * @param data - Les données à mettre à jour avec les nouvelles valeurs
 * @returns L'patient mis à jour
 */
export const updatePatient = createAsyncThunk(
  'patient/updatePatient',
  async ({ id, data }: { id: number; data: Patient }, { rejectWithValue }) => {
    try {

      const editPatientRepository = new EditPatientRepository()
      const response = await editPatientRepository.execute(id, data)
      return response
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR)
    }
  }
)

/*
 * @param id - L'ID du patientà supprimer
 * @returns L'ID du patient supprimé
 */
export const deletePatient = createAsyncThunk(
  'patient/deletePatient',
  async ({ id }: { id: number }, { rejectWithValue }) => {
    try {

      if (!id) {
        throw new Error('aucun patient sélectionné pour suppression.')
      }
      const deletePatientRepository = new DeletePatientRepository()
      await deletePatientRepository.execute(id)
      return id // Retourne l'ID supprimé
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR)
    }
  }
)

const patientSlice = createSlice({
  name: 'patient',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload
    },
    setSelectedPatient: (state, action: PayloadAction<Patient>) => {
      state.selectedPatient = action.payload
    },
    getPatientyId: (state, action: PayloadAction<number>) => {
      const matchingPatient = state.patients.find((patient) => patient.id === action.payload)[0]
      return matchingPatient
    },
    setIsAddPatientOpen: (state, action: PayloadAction<boolean>) => {
      state.isAddPatientOpen = action.payload
    },
    setIsEditPatientOpen: (state, action: PayloadAction<boolean>) => {
      state.isEditPatientOpen = action.payload
    },

    setIsRemovePatientOpen: (state, action: PayloadAction<boolean>) => {
      state.isRemovePatientOpen = action.payload
    },

    // Contrôler l'état des modales
    setModalState: (
      state,
      action: PayloadAction<{
        modalType: 'add' | 'edit' | 'remove'
        isOpen: boolean
      }>
    ) => {
      const { modalType, isOpen } = action.payload
      if (modalType == 'add') {
        state.isAddPatientOpen = isOpen
      }
      if (modalType == 'edit') {
        state.isEditPatientOpen = isOpen
      }
      if (modalType == 'remove') {
        state.isRemovePatientOpen = isOpen
      }
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getPatient.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getPatient.fulfilled, (state, action) => {
        state.loading = false
        state.patients = action.payload
      })
      .addCase(getPatient.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Ajout du patient
      .addCase(addPatients.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(addPatients.fulfilled, (state, action) => {
        state.patients.push(action.payload)
        state.loading = false
      })
      .addCase(addPatients.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Mise à jour du patient
      .addCase(updatePatient.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updatePatient.fulfilled, (state, action) => {
        const index = state.patients.findIndex((patient) => patient.id === action.payload.id)
        if (index !== -1) state.patients[index] = action.payload
        state.loading = false
        state.selectedPatient = null
      })
      .addCase(updatePatient.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Suppression du patient
      .addCase(deletePatient.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deletePatient.fulfilled, (state, action) => {
        state.patients = state.patients.filter((patient) => patient.id !== action.payload)
        state.selectedPatient = null
        state.loading = false
      })
      .addCase(deletePatient.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  }
})

export const {
  setError,
  setLoading,
  setSelectedPatient,
  getPatientyId,
  setIsAddPatientOpen,
  setIsEditPatientOpen,
  setIsRemovePatientOpen,
  setModalState
} = patientSlice.actions
export default patientSlice.reducer
