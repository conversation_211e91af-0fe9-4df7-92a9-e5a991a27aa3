import { Proche } from "@/domain/models";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface consultationFormData {
  dateRDV: string | null
  timeRDV: string | null
  categorie: string;
  speciality: string;
  consultationType: string;
  consultationReason: string;
  consultationMotif: string;
  phone: string;
  email: string;
  confirmEmail: string;
  password: string;
  acceptConditions: boolean;
  forWhom: string;
  procheInfo: Proche | null;
}

interface consultationStateSlice {
  activeStep: number;
  isPageValid: boolean;
  formData: consultationFormData;
}

const initialState: consultationStateSlice = {
  activeStep: 0,
  isPageValid: false,
  formData: {
    dateRDV: null,
    timeRDV: null,
    categorie: "",
    speciality: "",
    consultationType: "",
    consultationReason: "",
    consultationMotif: "",
    phone: "",
    email: "",
    confirmEmail: "",
    password: "",
    acceptConditions: false,
    forWhom: "",
    procheInfo: null,
  },
};

const consultationStateSlice = createSlice({
  name: "consultationStepper",
  initialState,
  reducers: {
    setActiveStep: (state, action: PayloadAction<number>) => {
      state.activeStep = action.payload;
    },
    setIsPageValid: (state, action: PayloadAction<boolean>) => {
      state.isPageValid = action.payload;
    },
    updateFormData: (state, action: PayloadAction<Partial<consultationFormData>>) => {
      state.formData = { ...state.formData, ...action.payload };
    },
    setProcheInfoField: (state, action: PayloadAction<Partial<Proche>>) => {
      state.formData.procheInfo = {
        ...state.formData.procheInfo,
        ...action.payload,
      };
    },
    resetAuth: (state) => {
      state.formData.email = "";
      state.formData.password = "";
    },
    resetState: (state) => {
      state.activeStep = 0;
      state.isPageValid = false;
      state.formData = {
        dateRDV: null,
        timeRDV: null,
        categorie: "",
        speciality: "",
        consultationType: "",
        consultationReason: "",
        consultationMotif: "",
        phone: "",
        email: "",
        confirmEmail: "",
        password: "",
        acceptConditions: false,
        forWhom: "",
        procheInfo: null,
      };
    },
  },
});

export const {
  setActiveStep,
  setIsPageValid,
  updateFormData,
  setProcheInfoField,
  resetAuth,
  resetState,
} = consultationStateSlice.actions;

export default consultationStateSlice.reducer;
