import { demande_adhesion, Utilisateur } from "@/domain/models";
import {
  CreateAdhesionRequestRepository,
  DeleteAdhesionRequestRepository,
  GetAdhesionRequestByEmailRepository,
  GetAdhesionRequestsRepository,
  MarkAdhesionRequestAsReadRepository,
  ApproveAdhesionRequestRepository,
  RejectAdhesionRequestRepository,
} from "@/infrastructure/repositories/AdhesionRequest";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  CreateAdhesionRequestUsecase,
  CheckEmailValidity,
  GetAdhesionRequestsUsecase,
  MarkAdhesionRequestAsReadUsecase,
  DeleteAdhesionRequestUsecase,
  ApproveAdhesionRequestUsecase,
  RejectAdhesionRequestUsecase,
} from "@/domain/usecases/adhesionRequest";
import { GetUserByEmailUsecase } from "@/domain/usecases/user/userUsecase";
import GetUserByEmailRepository from "@/infrastructure/repositories/user/GetUserByEmailRepository";
import { GetAdhesionRequestByIdRepository } from "@/infrastructure/repositories/AdhesionRequest";
import MembershipAcceptedEmailService from "@/services/customEmailService/MembershipAcceptedEmailService";
import CreateProfessionalInvitationRepository from "@/infrastructure/repositories/professionalInvitation/CreateProfessionalInvitationRepository";

interface AdhesionRequestState {
  requestAdhesionList: demande_adhesion[];
  selectedRequestAdhesion: demande_adhesion | null;
  loading: boolean;
  error: string | null;
  detailModalOpen: boolean;
  searchQuery: string;
  statusFilter: string;
}

const initialState: AdhesionRequestState = {
  requestAdhesionList: [],
  selectedRequestAdhesion: null,
  loading: false,
  error: null,
  detailModalOpen: false,
  searchQuery: "",
  statusFilter: "all",
};

const getAdhesionRequestsRepository = new GetAdhesionRequestsRepository();
const createAdhesionRequestRepository = new CreateAdhesionRequestRepository();
const deleteAdhesionRequestRepository = new DeleteAdhesionRequestRepository();
const markAdhesionRequestAsReadRepository =
  new MarkAdhesionRequestAsReadRepository();
const getUserByEmailRepository = new GetUserByEmailRepository();
const getUserByEmailUsecase = new GetUserByEmailUsecase(
  getUserByEmailRepository
);
const getAdhesionRequestByEmailRepository =
  new GetAdhesionRequestByEmailRepository();
const checkEmailValidity = new CheckEmailValidity(
  getUserByEmailUsecase,
  getAdhesionRequestByEmailRepository
);

const createAdhesionRequestUsecase = new CreateAdhesionRequestUsecase(
  createAdhesionRequestRepository,
  checkEmailValidity
);

const getAdhesionRequestsUsecase = new GetAdhesionRequestsUsecase(
  getAdhesionRequestsRepository
);

const markAdhesionRequestAsReadUsecase = new MarkAdhesionRequestAsReadUsecase(
  markAdhesionRequestAsReadRepository
);

const createProfessionalInvitationRepository =
  new CreateProfessionalInvitationRepository();

const membershipAcceptedEmailService = new MembershipAcceptedEmailService();

const deleteAdhesionRequestUsecase = new DeleteAdhesionRequestUsecase(
  deleteAdhesionRequestRepository
);

const getAdhesionRequestByIdRepository = new GetAdhesionRequestByIdRepository();
const approveAdhesionRequestRepository = new ApproveAdhesionRequestRepository();
const approveAdhesionRequestUsecase = new ApproveAdhesionRequestUsecase(
  approveAdhesionRequestRepository,
  getAdhesionRequestByIdRepository,
  createProfessionalInvitationRepository,
  membershipAcceptedEmailService
);

const rejectAdhesionRequestRepository = new RejectAdhesionRequestRepository();
const rejectAdhesionRequestUsecase = new RejectAdhesionRequestUsecase(
  rejectAdhesionRequestRepository
);

export const getAdhesionRequest = createAsyncThunk(
  "adhesionRequest/getAdhesionRequests",
  async (_, { rejectWithValue }) => {
    try {
      const data = await getAdhesionRequestsUsecase.execute();

      return data;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const createAdhesionRequest = createAsyncThunk(
  "adhesionRequest/create",
  async (
    adhesionData: Omit<demande_adhesion, "id" | "status" | "cree_a">,
    { rejectWithValue }
  ) => {
    try {
      const data = await createAdhesionRequestUsecase.execute(adhesionData);
      return data;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Erreur lors de la création de la demande. Réessayez plus tard";
      return rejectWithValue(errorMessage);
    }
  }
);

export const deleteAdhesionRequest = createAsyncThunk(
  "adhesionRequest/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      const deletedAdhestionRequest =
        await deleteAdhesionRequestUsecase.execute(id);

      return deletedAdhestionRequest;
    } catch (error) {
      return rejectWithValue(
        error.message ||
          "Erreur lors de la suppression de la demande d'adhésion du professionnel de santé"
      );
    }
  }
);

export const markAsRead = createAsyncThunk(
  "adhesionRequest/markAsRead",
  async (id: number, { rejectWithValue }) => {
    try {
      const updatedAdhesionRequest =
        await markAdhesionRequestAsReadUsecase.execute(id);

      return updatedAdhesionRequest;
    } catch (error) {
      return rejectWithValue(
        error.message ||
          "Erreur lors de la mise à jour de la demande d'adhésion professionnel de santé"
      );
    }
  }
);

export const approveAdhesionRequest = createAsyncThunk(
  "adhesionRequest/approve",
  async (id: number, { getState, rejectWithValue }) => {
    try {
      // Récupérer l'ID de l'administrateur connecté
      const state = getState() as {
        authentification: {
          user: Utilisateur | null;
          userData: unknown;
        };
      };

      const currentUser = state.authentification?.user;

      if (!currentUser) {
        throw new Error("Utilisateur non connecté");
      }

      const updatedAdhesionRequest =
        await approveAdhesionRequestUsecase.execute(id, currentUser);
      return updatedAdhesionRequest;
    } catch (error) {
      return rejectWithValue(
        error.message || "Erreur lors de l'approbation de la demande d'adhésion"
      );
    }
  }
);

export const rejectAdhesionRequest = createAsyncThunk(
  "adhesionRequest/reject",
  async (id: number, { rejectWithValue }) => {
    try {
      const updatedAdhesionRequest =
        await rejectAdhesionRequestUsecase.execute(id);
      return updatedAdhesionRequest;
    } catch (error) {
      return rejectWithValue(
        error.message || "Erreur lors du rejet de la demande d'adhésion"
      );
    }
  }
);

const adhesionRequestSlice = createSlice({
  name: "adhesionSlice",
  initialState,
  reducers: {
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    setSelectedRequest: (state, action: PayloadAction<demande_adhesion>) => {
      state.selectedRequestAdhesion = action.payload;
    },
    clearSelectedRequest: (state) => {
      state.selectedRequestAdhesion = null;
    },
    openDetailModal: (state) => {
      state.detailModalOpen = true;
    },
    closeDetailModal: (state) => {
      state.detailModalOpen = false;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setStatusFilter: (state, action: PayloadAction<string>) => {
      state.statusFilter = action.payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(createAdhesionRequest.pending, (state) => {
        state.loading = true;
        state.error = "";
      })
      .addCase(createAdhesionRequest.fulfilled, (state) => {
        state.loading = false;
        state.error = "";
      })
      .addCase(createAdhesionRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getAdhesionRequest.pending, (state) => {
        state.loading = true;
        state.error = "";
      })
      .addCase(
        getAdhesionRequest.fulfilled,
        (state, action: PayloadAction<demande_adhesion[]>) => {
          state.requestAdhesionList = action.payload;
          state.loading = false;
        }
      )
      .addCase(getAdhesionRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(deleteAdhesionRequest.pending, (state) => {
        state.loading = true;
        state.error = "";
      })
      .addCase(deleteAdhesionRequest.fulfilled, (state, action) => {
        state.requestAdhesionList = state.requestAdhesionList.filter(
          (request) => request.id != action.payload.id
        );
        state.loading = false;
      })
      .addCase(deleteAdhesionRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(markAsRead.pending, (state) => {
        state.loading = true;
        state.error = "";
      })
      .addCase(markAsRead.fulfilled, (state, action) => {
        const matchingRequest = state.requestAdhesionList.filter(
          (request) => request.id === action.payload.id
        )[0];
        matchingRequest.status = action.payload.status;
      })
      .addCase(markAsRead.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      .addCase(approveAdhesionRequest.pending, (state) => {
        state.loading = true;
        state.error = "";
      })
      .addCase(approveAdhesionRequest.fulfilled, (state, action) => {
        const matchingRequest = state.requestAdhesionList.filter(
          (request) => request.id === action.payload.id
        )[0];
        matchingRequest.status = action.payload.status;
        state.loading = false;
      })
      .addCase(approveAdhesionRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(rejectAdhesionRequest.pending, (state) => {
        state.loading = true;
        state.error = "";
      })
      .addCase(rejectAdhesionRequest.fulfilled, (state, action) => {
        const matchingRequest = state.requestAdhesionList.filter(
          (request) => request.id === action.payload.id
        )[0];
        matchingRequest.status = action.payload.status;
        state.loading = false;
      })
      .addCase(rejectAdhesionRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      }),
});

export const {
  setIsLoading,
  setError,
  setSelectedRequest,
  clearSelectedRequest,
  openDetailModal,
  closeDetailModal,
  setSearchQuery,
  setStatusFilter,
} = adhesionRequestSlice.actions;
export default adhesionRequestSlice.reducer;
