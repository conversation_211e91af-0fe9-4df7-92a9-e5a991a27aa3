import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { Vaccination } from '@/domain/models'
import { 
  CreateVaccinationUseCase,
  GetVaccinationUseCase,
  UpdateVaccinationUseCase,
  DeleteVaccinationUseCase
} from '@/domain/usecases/professional/vaccination'
import { CreateVaccinationRepository, DeleteVaccinationRepository, GetVaccinationRepository, UpdateVaccinationRepository } from '@/infrastructure/repositories/vaccination'

interface VaccinationSliceState {
  vaccinations: Vaccination[]
  selectedVaccination: Vaccination | null
  vaccinationState: {
    dateAdministration: { [key: string]: string | null };
    prochaineDate: { [key: string]: string | null };
    remarks: { [key: string]: string },
  },
  loading: boolean
  error: string | null
}

const DEFAULT_VACCINATION_STATE = {
  dateAdministration: {},
  prochaineDate: {},
  remarks: {},
}

const initialState: VaccinationSliceState = {
  vaccinations: [],
  selectedVaccination: null,
  vaccinationState: DEFAULT_VACCINATION_STATE,
  loading: false,
  error: null
}

export const createVaccination = createAsyncThunk(
  'vaccination/create',
  async (data: Omit<Vaccination, "id">[], { rejectWithValue }) => {
    try {
      const createVaccinationRepository = new CreateVaccinationRepository()
      const createVaccinationUseCase = new CreateVaccinationUseCase(createVaccinationRepository)
      const result = await createVaccinationUseCase.execute(data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const getVaccinations = createAsyncThunk(
  'vaccination/getAll',
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getVaccinationRepository = new GetVaccinationRepository()
      const getVaccinationUseCase = new GetVaccinationUseCase(getVaccinationRepository)
      const result = await getVaccinationUseCase.getAll(carnetId)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const updateVaccination = createAsyncThunk(
  'vaccination/update',
  async ({ id, data }: { id: number; data: Partial<Vaccination> }, { rejectWithValue }) => {
    try {
      const updateVaccinationRepository = new UpdateVaccinationRepository()
      const updateVaccinationUseCase = new UpdateVaccinationUseCase(updateVaccinationRepository)
      const result = await updateVaccinationUseCase.execute(id, data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const deleteVaccination = createAsyncThunk(
  'vaccination/delete',
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteVaccinationRepository = new DeleteVaccinationRepository()
      const deleteVaccinationUseCase = new DeleteVaccinationUseCase(deleteVaccinationRepository)
      await deleteVaccinationUseCase.execute(id)
      return id
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

const vaccinationSlice = createSlice({
  name: 'vaccination',
  initialState,
  reducers: {
    setSelectedVaccination: (state, action) => {
      state.selectedVaccination = action.payload
    },
    setDateAdministration: (state, action: PayloadAction<{item: string, value: string | null}>) => {
      state.vaccinationState.dateAdministration = {
        ...state.vaccinationState.dateAdministration,
        [action.payload.item]: action.payload.value,
      }
    },
    setProchaineDate: (state, action: PayloadAction<{item: string, value: string | null}>) => {
      state.vaccinationState.prochaineDate = {
        ...state.vaccinationState.prochaineDate,
        [action.payload.item]: action.payload.value,
      }
    },
    setRemarks: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.vaccinationState.remarks = {
        ...state.vaccinationState.remarks,
        [action.payload.item]: action.payload.value,
      }
    },
    resetVaccinationState: (state) => {
      state.vaccinationState = DEFAULT_VACCINATION_STATE
    },
    clearSelectedVaccination: (state) => {
      state.selectedVaccination = null
    }
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createVaccination.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createVaccination.fulfilled, (state, action) => {
        state.loading = false
        state.vaccinations.push(...action.payload)
      })
      .addCase(createVaccination.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Get All
      .addCase(getVaccinations.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getVaccinations.fulfilled, (state, action) => {
        state.loading = false
        state.vaccinations = action.payload
      })
      .addCase(getVaccinations.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Update
      .addCase(updateVaccination.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateVaccination.fulfilled, (state, action) => {
        state.loading = false
        const index = state.vaccinations.findIndex(v => v.id === action.payload.id)
        if (index !== -1) {
          state.vaccinations[index] = action.payload
        }
      })
      .addCase(updateVaccination.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Delete
      .addCase(deleteVaccination.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteVaccination.fulfilled, (state, action) => {
        state.loading = false
        state.vaccinations = state.vaccinations.filter(v => v.id !== Number(action.payload))
      })
      .addCase(deleteVaccination.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  }
})

export const {
  setSelectedVaccination,
  setDateAdministration,
  setProchaineDate,
  setRemarks,
  resetVaccinationState,
  clearSelectedVaccination
} = vaccinationSlice.actions
export default vaccinationSlice.reducer
