import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { laboratoire_diagnostics } from '@/domain/models'

import { CreateDiagnosticRepository, DeleteDiagnosticRepository, GetAllDiagnosticRepository, GetOldPathRepository, UpdateDiagnosticRepository } from '@/infrastructure/repositories/diagnostics'
import { CreateDiagnosticUseCase, DeleteDiagnosticUseCase, GetAllDiagnosticUseCase, UpdateDiagnosticUseCase } from '@/domain/usecases/professional/diagnostic'
import { DeleteImageService } from '@/domain/services/DeleteImageService'
import { DeleteImageRepository, GetPublicUrlRepository, UploadRepository } from '@/infrastructure/repositories/uploadFile'
import { UploadServices } from '@/domain/services/UploadService'
interface DiagnosticSliceState {
  diagnostics: laboratoire_diagnostics[]
  selectedDiagnosticSlice: laboratoire_diagnostics | null
  diagnosticState: {
    titre: string;
    type_fichier: string;
    impression_resultat: string;
    path: string;
    selectedFile: File | null;
    remarks: string;
  }
  loading: boolean
  error: string | null
}

const DEFAULT_DIAGNOSTIC_STATE = {
  titre: "",
  type_fichier: "",
  impression_resultat: "",
  path: "",
  selectedFile: null,
  remarks: "",
}

const initialState: DiagnosticSliceState = {
  diagnostics: [],
  selectedDiagnosticSlice: null,
  diagnosticState: DEFAULT_DIAGNOSTIC_STATE,
  loading: false,
  error: null
}

const deleteImageRepository = new DeleteImageRepository()
const deleteImageService = new DeleteImageService(
  deleteImageRepository
)
const getOldPathRepository = new GetOldPathRepository()
const uploadRepository = new UploadRepository()
const getPublicUrlRepository = new GetPublicUrlRepository()
const uploadServices = new UploadServices(
  uploadRepository,
  getPublicUrlRepository
)

export const createDiagnosticSlice = createAsyncThunk(
  'diagnostic/create',
  async (data: Omit<laboratoire_diagnostics, "id">, { rejectWithValue }) => {
    try {
      const createDiagnosticRepository = new CreateDiagnosticRepository()
      const createDiagnosticUseCase = new CreateDiagnosticUseCase(createDiagnosticRepository)
      const result = await createDiagnosticUseCase.execute(data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const getAllDiagnosticSlices = createAsyncThunk(
  'diagnostic/getAll',
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getAllDiagnosticRepository = new GetAllDiagnosticRepository()
      const getAllDiagnosticUseCase = new GetAllDiagnosticUseCase(getAllDiagnosticRepository)
      const result = await getAllDiagnosticUseCase.getAll(carnetId)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const updateDiagnosticSlice = createAsyncThunk(
  'diagnostic/update',
  async ({ id, data, selectedFile }: { id: number; data: Partial<laboratoire_diagnostics>, selectedFile: File | null }, { rejectWithValue }) => {
    try {
      const updateDiagnosticRepository = new UpdateDiagnosticRepository()
      const updateDiagnosticUseCase = new UpdateDiagnosticUseCase(
        deleteImageService,
        uploadServices,
        updateDiagnosticRepository
      )
      const result = await updateDiagnosticUseCase.execute(id, data, selectedFile)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const deleteDiagnosticSlice = createAsyncThunk(
  'diagnostic/delete',
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteDiagnosticRepository = new DeleteDiagnosticRepository()
      const deleteDiagnosticUseCase = new DeleteDiagnosticUseCase(
        getOldPathRepository,
        deleteDiagnosticRepository,
        deleteImageService
      )

      await deleteDiagnosticUseCase.execute(id)
      return id
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

const diagnosticSlice = createSlice({
  name: 'diagnostic',
  initialState,
  reducers: {
    setSelectedDiagnosticSlice: (state, action) => {
      state.selectedDiagnosticSlice = action.payload
    },
    setTitle: (state, action: PayloadAction<string>) => {
      state.diagnosticState.titre = action.payload
    },
    setTypeFichier: (state, action: PayloadAction<string>) => {
      state.diagnosticState.type_fichier = action.payload
    },
    setSelectedFile: (state, action) => {
      state.diagnosticState.selectedFile = action.payload;
    },
    setImpressionResultat: (state, action: PayloadAction<string>) => {
      state.diagnosticState.impression_resultat = action.payload
    },
    setPath: (state, action: PayloadAction<string>) => {
      state.diagnosticState.path = action.payload
    },
    setRemarks: (state, action: PayloadAction<string>) => {
      state.diagnosticState.remarks = action.payload
    },
    resetDiagnosticState: (state) => {
      state.diagnosticState = DEFAULT_DIAGNOSTIC_STATE
    },
    clearSelectedDiagnosticSlice: (state) => {
      state.selectedDiagnosticSlice = null
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createDiagnosticSlice.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createDiagnosticSlice.fulfilled, (state, action) => {
        state.loading = false
        state.diagnostics = [...state.diagnostics, action.payload]
      })
      .addCase(createDiagnosticSlice.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Get All
      .addCase(getAllDiagnosticSlices.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getAllDiagnosticSlices.fulfilled, (state, action) => {
        state.loading = false
        state.diagnostics = action.payload
      })
      .addCase(getAllDiagnosticSlices.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Update
      .addCase(updateDiagnosticSlice.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateDiagnosticSlice.fulfilled, (state, action) => {
        state.loading = false
        const index = state.diagnostics.findIndex(am => am.id === action.payload.id)
        if (index !== -1) {
          state.diagnostics[index] = action.payload
        }
      })
      .addCase(updateDiagnosticSlice.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Delete
      .addCase(deleteDiagnosticSlice.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteDiagnosticSlice.fulfilled, (state, action) => {
        state.loading = false
        state.diagnostics = state.diagnostics.filter(am => am.id !== Number(action.payload))
      })
      .addCase(deleteDiagnosticSlice.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  }
})

export const {
  setSelectedDiagnosticSlice,
  setTitle,
  setTypeFichier,
  setImpressionResultat,
  setSelectedFile,
  setPath,
  setRemarks,
  resetDiagnosticState,
  clearSelectedDiagnosticSlice,
} = diagnosticSlice.actions
export default diagnosticSlice.reducer
