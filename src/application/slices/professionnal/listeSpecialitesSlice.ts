import { ListeSpecialites } from "@/domain/models";
import GetSpecialitiesUsecase from "@/domain/usecases/professionalSpecialities/GetSpecialitiesUsecase";
import GetSpecialitiesRepository from "@/infrastructure/repositories/professionalSpecialities/GetSpecialitiesRepository";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ListeSpecialitesSlice {
  listes: ListeSpecialites[];
  loading: boolean;
  error: string | null;
  isPopoverOpen: boolean;
}

const initialState: ListeSpecialitesSlice = {
  listes: [],
  loading: false,
  error: null,
  isPopoverOpen: false,
};

export const getSpecialities = createAsyncThunk(
  "listeSpecialites/getSpecialities",
  async (_, { rejectWithValue }) => {
    try {
      const getSpecialitiesRepository = new GetSpecialitiesRepository();
      const getSpecialitiesUsecase = new GetSpecialitiesUsecase(
        getSpecialitiesRepository
      );
      const data = await getSpecialitiesUsecase.execute();

      return data;
    } catch (error) {
      return rejectWithValue(error.message || "Erreur");
    }
  }
);

const listeSpecialitesSlice = createSlice({
  name: "listeSpecialites",
  initialState,
  reducers: {
    setIsPopOverOpen(state, action: PayloadAction<boolean>) {
      state.isPopoverOpen = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSpecialities.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSpecialities.fulfilled, (state, action) => {
        state.loading = false;
        state.listes = action.payload;
      })
      .addCase(getSpecialities.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setIsPopOverOpen } = listeSpecialitesSlice.actions;

export default listeSpecialitesSlice.reducer;
