import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { Patient, ProfessionnelPatient, Urgence } from "@/domain/models";
import {
  CreateProfessionalPatientDTO,
  ProfessionnelPatientDTO,
} from "@/domain/DTOS";
import { GetProfessionalPatientRepository } from "@/infrastructure/repositories/ProfesionnalPatient/GetProfessionalPatientRepository";
import {
  DeleteProfessionalPatientUsecase,
  GetProfessionalPatientUsecase,
  UpdateProfessionalPatientUsecase,
} from "@/domain/usecases/professional/professionnelPatient";
import { GetProfessionalPatientByIdUsecase } from "@/domain/usecases/professional/professionnelPatient/GetProfessionalPatientByIdUsecase";
import { GetProfessionalPatientByIdRepository } from "@/infrastructure/repositories/ProfesionnalPatient/GetProfessionalPatientByIdRepository";
import {
  CreateProfessionalPatientRepository,
  DeleteProfessionalPatientRepository,
  UpdateProfessionalPatientRepository,
} from "@/infrastructure/repositories/ProfesionnalPatient";
import { MatriculeGenerator } from "@/domain/services/MatriculeGenerator";
import { CreateProfessionalPatientUsecase } from "@/domain/usecases/professional/professionnelPatient/CreateProfessionalPatientUsecase";
import CreatePatientRepository from "@/infrastructure/repositories/patients/CreatePatientRepository";
import GetProfessionalPatientByPatientIdRepository from "@/infrastructure/repositories/ProfesionnalPatient/GetProfessionalPatientByPatientIdRepository";
import GetProfessionalPatientByPatientIdUsecase from "@/domain/usecases/professional/professionnelPatient/GetProfessionalPatientByPatientIdUsecase";
import RegisterProfessionalPatientUsecase from "@/domain/usecases/user/Register/RegisterProfessionalPatientUsecase";

interface ProfessionalPatientState {
  selectedProfessionalPatient: ProfessionnelPatient | null;
  selectedDataProfessionalPatient: ProfessionnelPatientDTO | null;
  dataProfessionalPatients: ProfessionnelPatientDTO[] | null;
  professionalPatients: ProfessionnelPatient[] | null;
  loading: boolean;
  error: string | null;
}

const initialState: ProfessionalPatientState = {
  selectedProfessionalPatient: null,
  selectedDataProfessionalPatient: null,
  dataProfessionalPatients: [],
  professionalPatients: [],
  loading: false,
  error: null,
};

const getProfessionalPatientRepository = new GetProfessionalPatientRepository();
const getProfessionalPatientUsecase = new GetProfessionalPatientUsecase(
  getProfessionalPatientRepository,
);

const getProfessionalPatientByIdRepository =
  new GetProfessionalPatientByIdRepository();
const getProfessionalPatientByIdUsecase = new GetProfessionalPatientByIdUsecase(
  getProfessionalPatientByIdRepository,
);

const getProfessionalPatientByPatientIdRepository =
  new GetProfessionalPatientByPatientIdRepository();
const getProfessionalPatientByPatientIdUsecase =
  new GetProfessionalPatientByPatientIdUsecase(
    getProfessionalPatientByIdRepository,
  );

const matriculeGenerator = new MatriculeGenerator();
const createPatientRepository = new CreatePatientRepository();
const createProfessionalPatientRepository =
  new CreateProfessionalPatientRepository();

const createProfessionalPatientUsecase = new CreateProfessionalPatientUsecase(
  createProfessionalPatientRepository,
);

const registerProfessionalPatientUsecase =
  new RegisterProfessionalPatientUsecase(
    createPatientRepository,
    createProfessionalPatientRepository,
    matriculeGenerator,
  );

const deleteProfessionalPatientRepository =
  new DeleteProfessionalPatientRepository();
const deleteProfessionalPatientUsecase = new DeleteProfessionalPatientUsecase(
  deleteProfessionalPatientRepository,
);

const updateProfessionalPatientRepository =
  new UpdateProfessionalPatientRepository();
const updateProfessionalPatientUsecase = new UpdateProfessionalPatientUsecase(
  updateProfessionalPatientRepository,
);

export const fetchProfessionalPatient = createAsyncThunk(
  "professionalPatient/fetchProfessionalPatient",
  async (id: number, { rejectWithValue }) => {
    try {
      return await getProfessionalPatientUsecase.execute(id);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  },
);

export const fetchProfessionalPatientById = createAsyncThunk(
  "professionalPatient/fetchProfessionalPatientById",
  async (id: number, { rejectWithValue }) => {
    try {
      return await getProfessionalPatientByIdUsecase.execute(id);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  },
);

export const fetchProfessionalPatientByPatientId = createAsyncThunk(
  "professionalPatient/fetchProfessionalPatientByPatientId",
  async (id: number, { rejectWithValue }) => {
    try {
      return await getProfessionalPatientByPatientIdUsecase.execute(id);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  },
);

export const createProfessionalPatient = createAsyncThunk(
  "professionalPatient/create",
  async (
    data: Omit<ProfessionnelPatient, "id">,
    { rejectWithValue },
  ) => {
    try {
      return await createProfessionalPatientUsecase.execute(
        data,
      );
    } catch (error) {
      return rejectWithValue(error.message);
    }
  },
);

export const registerProfessionalPatient = createAsyncThunk(
  "professionalPatient/register",
  async (
    data: CreateProfessionalPatientDTO,
    { rejectWithValue },
  ) => {
    try {
      return await registerProfessionalPatientUsecase.execute(
        data,
      );
    } catch (error) {
      return rejectWithValue(error.message);
    }
  },
);

export const updateProfessionalPatient = createAsyncThunk(
  "professionalPatient/update",
  async (
    {
      id,
      data,
    }: {
      id: number;
      data: Partial<ProfessionnelPatient>;
    },
    { rejectWithValue },
  ) => {
    try {
      return await updateProfessionalPatientUsecase.execute(id, data);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  },
);

export const deleteProfessionalPatient = createAsyncThunk(
  "professionalPatient/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      return await deleteProfessionalPatientUsecase.execute(id);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  },
);

const professionalPatientSlice = createSlice({
  name: "professionalPatient",
  initialState,
  reducers: {
    clearCurrentProfessionalPatient: (state) => {
      state.selectedProfessionalPatient = null;
    },
    setSelectedDataProfessionalPatient: (state, action: PayloadAction<Patient & {
      avatar?: string;
      urgence?: Urgence[];
    }>) => {
      state.selectedDataProfessionalPatient.patient = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch professionnel patient
    builder
      .addCase(fetchProfessionalPatient.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProfessionalPatient.fulfilled, (state, action) => {
        state.loading = false;
        state.dataProfessionalPatients = action.payload;
      })
      .addCase(fetchProfessionalPatient.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ||
          "Failed to fetch professionnel patient";
      })
      .addCase(fetchProfessionalPatientById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProfessionalPatientById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedDataProfessionalPatient = action.payload;
      })
      .addCase(fetchProfessionalPatientById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ||
          "Failed to fetch professionnel patient";
      })
      .addCase(fetchProfessionalPatientByPatientId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchProfessionalPatientByPatientId.fulfilled,
        (state, action) => {
          state.loading = false;
          state.selectedDataProfessionalPatient = action.payload;
        },
      )
      .addCase(
        fetchProfessionalPatientByPatientId.rejected,
        (state, action) => {
          state.loading = false;
          state.error = action.error.message ||
            "Failed to fetch professionnel patient";
        },
      );

    // Create professionnel patient
    builder
      .addCase(createProfessionalPatient.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createProfessionalPatient.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          state.professionalPatients = [action.payload || null];
        }
      })
      .addCase(createProfessionalPatient.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ||
          "Erreur lors de la creation du professionnel patient";
      })
      .addCase(registerProfessionalPatient.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerProfessionalPatient.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          state.professionalPatients = [
            ...(state.professionalPatients || []),
            action.payload,
          ];
        }
      })
      .addCase(registerProfessionalPatient.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ||
          "Erreur lors de la creation du professionnel patient";
      });

    // Update professionnel patient
    builder
      .addCase(updateProfessionalPatient.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateProfessionalPatient.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedProfessionalPatient = action.payload;
        if (action.payload) {
          const index = state.professionalPatients.findIndex(
            (p) => p.id === action.payload.id,
          );
          if (index !== -1) {
            state.professionalPatients[index] = action.payload;
          }
        }
      })
      .addCase(updateProfessionalPatient.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ||
          "Failed to update professionnel patient";
      });

    // Delete professionnel patient
    builder
      .addCase(deleteProfessionalPatient.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteProfessionalPatient.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          state.dataProfessionalPatients = state.dataProfessionalPatients
            .filter(
              (p) => p.id !== action.payload.id,
            );
          if (state.selectedProfessionalPatient?.id === action.payload.id) {
            state.selectedProfessionalPatient = null;
          }
        }
      })
      .addCase(deleteProfessionalPatient.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ||
          "Failed to delete professionnel patient";
      });
  },
});

export const { clearCurrentProfessionalPatient, setSelectedDataProfessionalPatient, clearError } =
  professionalPatientSlice.actions;
export default professionalPatientSlice.reducer;
