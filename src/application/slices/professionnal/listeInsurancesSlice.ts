import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { SupabaseError } from "@/infrastructure/supabase/supabaseError";
import GetProfessionalInsurancesRepository from "@/infrastructure/repositories/insurance/GetProfessionalInsurancesRepository";
import GetProfessionalInsurancesUsecase from "@/domain/usecases/insurance/GetProfessionalInsurancesUsecase";
import GetInsurancesRepository from "@/infrastructure/repositories/insurance/GetInsurancesRepository";
import GetInsurancesUsecase from "@/domain/usecases/insurance/GetInsurancesUsecase";
import { ListeAssurances } from "@/domain/models";

interface InsurancesState {
  listes: ListeAssurances[];
  loading: boolean;
  error: string | null;
}

const initialState: InsurancesState = {
  listes: [],
  loading: false,
  error: null,
};

const getProfessionalInsurancesRepository =
  new GetProfessionalInsurancesRepository();
const getProfessionalInsurancesUsecase = new GetProfessionalInsurancesUsecase(
  getProfessionalInsurancesRepository
);

const getInsurancesRepository = new GetInsurancesRepository();
const getInsurancesUsecase = new GetInsurancesUsecase(getInsurancesRepository);

export const getProfessionalInsurances = createAsyncThunk(
  "insurances/getProfessionalInsurances",
  async (professional_id: number, { rejectWithValue }) => {
    try {
      return getProfessionalInsurancesUsecase.execute(professional_id);
    } catch (error) {
      if (error instanceof SupabaseError) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue(
        "Une erreur est survenue lors de la récupération des assurances"
      );
    }
  }
);

export const getInsurances = createAsyncThunk(
  "insurances/getInsurances",
  async (_, { rejectWithValue }) => {
    try {
      return getInsurancesUsecase.execute();
    } catch (error) {
      if (error instanceof SupabaseError) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue(
        "Une erreur est survenue lors de la récupération des assurances"
      );
    }
  }
);

const listeInsurancesSlice = createSlice({
  name: "listeInsurances",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getInsurances.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getInsurances.fulfilled, (state, action) => {
        state.loading = false;
        state.listes = action.payload;
      })
      .addCase(getInsurances.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default listeInsurancesSlice.reducer;
