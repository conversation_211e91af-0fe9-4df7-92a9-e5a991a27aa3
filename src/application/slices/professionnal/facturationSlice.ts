import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { Facturation } from "@/domain/models";
import {
  GetFacturationRepository,
  GetFacturationsByProfessionalIdRepository,
  GetFacturationByPatientIdRepository,
  CreateFacturationRepository,
  UpdateFacturationRepository,
  DeleteFacturationRepository,
} from "@/infrastructure/repositories/facturation";

import { FacturationDTO } from "@/domain/DTOS";
import { GetFacturationUsecase } from "@/domain/usecases/professional/facturation/GetFacturationRepositoryUsecase";
import { GetFacturationsByProfessionalIdUsecase } from "@/domain/usecases/professional/facturation/GetFacturationsByProfessionalIdUsecase";
import { GetFacturationByPatientIdUsecase } from "@/domain/usecases/professional/facturation/GetFacturationsByPatientIdUsecase";
import { CreateFacturationUsecase } from "@/domain/usecases/professional/facturation/CreateFacturationUsecase";
import { UpdateFacturationUsecase } from "@/domain/usecases/professional/facturation/UpdateFacturationUsecase";
import { DeleteFacturationUsecase } from "@/domain/usecases/professional/facturation/DeleteFacturationUsecase";

interface FacturationState {
  facturations: Facturation[];
  currentFacturation: Facturation | null;
  listeFacturationProfessional: FacturationDTO[] | null;
  listeFacturationPatient: FacturationDTO[] | null;
  loading: boolean;
  error: string | null;
}

const initialState: FacturationState = {
  facturations: [],
  listeFacturationProfessional: [],
  listeFacturationPatient: [],
  currentFacturation: null,
  loading: false,
  error: null,
};

export const fetchFacturationById = createAsyncThunk(
  "facturation/fetchById",
  async (id: number, { rejectWithValue }) => {
    try {
      const getFacturationRepository = new GetFacturationRepository();
      const getFacturationUsecase = new GetFacturationUsecase(
        getFacturationRepository
      );
      return await getFacturationUsecase.execute(id);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchFacturationsByProfessionalId = createAsyncThunk(
  "facturation/fetchByProfessionalId",
  async (professionalId: number, { rejectWithValue }) => {
    try {
      const getFacturationsByProfessionalIdRepository =
        new GetFacturationsByProfessionalIdRepository();
      const getFacturationsByProfessionalIdUsecase =
        new GetFacturationsByProfessionalIdUsecase(
          getFacturationsByProfessionalIdRepository
        );
      return await getFacturationsByProfessionalIdUsecase.execute(
        professionalId
      );
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchFacturationsByPatientId = createAsyncThunk(
  "facturation/fetchByPatientId",
  async (patientId: number, { rejectWithValue }) => {
    try {
      const getFacturationsByPatientIdRepository =
        new GetFacturationByPatientIdRepository();
      const getFacturationsByPatientIdUsecase =
        new GetFacturationByPatientIdUsecase(
          getFacturationsByPatientIdRepository
        );
      return await getFacturationsByPatientIdUsecase.execute(patientId);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const createFacturation = createAsyncThunk(
  "facturation/create",
  async (facturation: Omit<Facturation, "id">, { rejectWithValue }) => {
    try {
      const createFacturationRepository = new CreateFacturationRepository();
      const createFacturationUsecase = new CreateFacturationUsecase(
        createFacturationRepository
      );
      return await createFacturationUsecase.execute(facturation);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateFacturation = createAsyncThunk(
  "facturation/update",
  async (
    {
      id,
      facturation,
    }: {
      id: number;
      facturation: Partial<Facturation>;
    },
    { rejectWithValue }
  ) => {
    try {
      const updateFacturationRepository = new UpdateFacturationRepository();
      const updateFacturationUsecase = new UpdateFacturationUsecase(
        updateFacturationRepository
      );
      return await updateFacturationUsecase.execute(id, facturation);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const deleteFacturation = createAsyncThunk(
  "facturation/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteFacturationRepository = new DeleteFacturationRepository();
      const deleteFacturationUsecase = new DeleteFacturationUsecase(
        deleteFacturationRepository
      );
      return await deleteFacturationUsecase.execute(id);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const facturationSlice = createSlice({
  name: "facturation",
  initialState,
  reducers: {
    clearCurrentFacturation: (state) => {
      state.currentFacturation = null;
    },
    setSelectedConsultation: (state, action) => {
      state.currentFacturation = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch facturation by ID
    builder
      .addCase(fetchFacturationById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFacturationById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentFacturation = action.payload;
      })
      .addCase(fetchFacturationById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch facturation";
      });

    // Fetch facturations by professional ID
    builder
      .addCase(fetchFacturationsByProfessionalId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFacturationsByProfessionalId.fulfilled, (state, action) => {
        state.loading = false;
        state.listeFacturationProfessional = action.payload;
      })
      .addCase(fetchFacturationsByProfessionalId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch facturations";
      });

    // Fetch facturations by patient ID
    builder
      .addCase(fetchFacturationsByPatientId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFacturationsByPatientId.fulfilled, (state, action) => {
        state.loading = false;
        state.listeFacturationPatient = action.payload;
      })
      .addCase(fetchFacturationsByPatientId.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.error.message || "Failed to fetch facturations by patient";
      });

    // Create medical facturation
    builder
      .addCase(createFacturation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createFacturation.fulfilled, (state, action) => {
        state.loading = false;
        state.currentFacturation = action.payload;
        if (action.payload) {
          state.facturations.push(action.payload);
        }
      })
      .addCase(createFacturation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to create facturation";
      });

    // Update medical facturation
    builder
      .addCase(updateFacturation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateFacturation.fulfilled, (state, action) => {
        state.loading = false;
        state.currentFacturation = action.payload;
        if (action.payload) {
          const index = state.facturations.findIndex(
            (c) => c.id === action.payload.id
          );
          if (index !== -1) {
            state.facturations[index] = action.payload;
          }
        }
      })
      .addCase(updateFacturation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to update facturation";
      });

    // Delete medical facturation
    builder
      .addCase(deleteFacturation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteFacturation.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          state.facturations = state.facturations.filter(
            (c) => c.id !== action.payload.id
          );
          if (state.currentFacturation?.id === action.payload.id) {
            state.currentFacturation = null;
          }
        }
      })
      .addCase(deleteFacturation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to delete facturation";
      });
  },
});

export const { clearCurrentFacturation, setSelectedConsultation, clearError } =
  facturationSlice.actions;
export default facturationSlice.reducer;
