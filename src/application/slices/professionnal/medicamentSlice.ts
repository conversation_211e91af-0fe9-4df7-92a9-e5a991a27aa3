import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { Medicament } from '@/domain/models'
import { CreateMedicamentRepository, DeleteMedicamentRepository, GetAllMedicamentRepository, UpdateMedicamentRepository } from '@/infrastructure/repositories/medicament'
import { CreateMedicamentUseCase, DeleteMedicamentUseCase, GetAllMedicamentUseCase, UpdateMedicamentUseCase } from '@/domain/usecases/professional/medicament'

interface MedicamentSliceState {
  medicaments: Medicament[]
  selectedMedicamentSlice: Medicament | null
  medicamentState: {
    force: { [key: string]: string },
    posologie: { [key: string]: string },
    duree: { [key: string]: number },
    typeConsommation: { [key: string]: string },
    frequence: { [key: string]: string },
    quantite: { [key: string]: string },
    calendrier: { [key: string]: string },
    remarks: { [key: string]: string },
  },
  loading: boolean
  error: string | null
}

const DEFAULT_MEDICAMENT_STATE = {
  force: {},
  posologie: {},
  duree: {},
  typeConsommation: {},
  frequence: {},
  quantite: {},
  calendrier: {},
  remarks: {},
}

const initialState: MedicamentSliceState = {
  medicaments: [],
  selectedMedicamentSlice: null,
  medicamentState: DEFAULT_MEDICAMENT_STATE,
  loading: false,
  error: null
}

export const createMedicamentSlice = createAsyncThunk(
  'medicament/create',
  async (data: Omit<Medicament, "id">[], { rejectWithValue }) => {
    try {
      const createMedicamentRepository = new CreateMedicamentRepository()
      const createMedicamentUseCase = new CreateMedicamentUseCase(createMedicamentRepository)
      const result = await createMedicamentUseCase.execute(data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const getAllMedicamentSlices = createAsyncThunk(
  'medicament/getAll',
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getAllMedicamentRepository = new GetAllMedicamentRepository()
      const getAllMedicamentUseCase = new GetAllMedicamentUseCase(getAllMedicamentRepository)
      const result = await getAllMedicamentUseCase.getAll(carnetId)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const updateMedicamentSlice = createAsyncThunk(
  'medicament/update',
  async ({ id, data }: { id: number; data: Partial<Medicament> }, { rejectWithValue }) => {
    try {
      const updateMedicamentRepository = new UpdateMedicamentRepository()
      const updateMedicamentUseCase = new UpdateMedicamentUseCase(updateMedicamentRepository)
      const result = await updateMedicamentUseCase.execute(id, data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const deleteMedicamentSlice = createAsyncThunk(
  'medicament/delete',
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteMedicamentRepository = new DeleteMedicamentRepository()
      const deleteMedicamentUseCase = new DeleteMedicamentUseCase(deleteMedicamentRepository)
      await deleteMedicamentUseCase.execute(id)
      return id
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

const medicamentSlice = createSlice({
  name: 'medicament',
  initialState,
  reducers: {
    setSelectedMedicamentSlice: (state, action) => {
      state.selectedMedicamentSlice = action.payload
    },
    setForce: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.medicamentState.force = {
        ...state.medicamentState.force,
        [action.payload.item]: action.payload.value,
      }
    },
    setPosologie: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.medicamentState.posologie = {
        ...state.medicamentState.posologie,
        [action.payload.item]: action.payload.value,
      }
    },
    setDuree: (state, action: PayloadAction<{item: string, value: number}>) => {
      state.medicamentState.duree = {
        ...state.medicamentState.duree,
        [action.payload.item]: action.payload.value,
      }
    },
    setTypeConsommation: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.medicamentState.typeConsommation = {
        ...state.medicamentState.typeConsommation,
        [action.payload.item]: action.payload.value,
      }
    },
    setFrequence: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.medicamentState.frequence = {
        ...state.medicamentState.frequence,
        [action.payload.item]: action.payload.value,
      }
    },
    setQuantite: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.medicamentState.quantite = {
        ...state.medicamentState.quantite,
        [action.payload.item]: action.payload.value,
      }
    },
    setCalendrier: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.medicamentState.calendrier = {
        ...state.medicamentState.calendrier,
        [action.payload.item]: action.payload.value,
      }
    },
    setRemarks: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.medicamentState.remarks = {
        ...state.medicamentState.remarks,
        [action.payload.item]: action.payload.value,
      }
    },
    resetMedicamentState: (state) => {
      state.medicamentState = DEFAULT_MEDICAMENT_STATE
    },
    clearSelectedMedicamentSlice: (state) => {
      state.selectedMedicamentSlice = null
    }
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createMedicamentSlice.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createMedicamentSlice.fulfilled, (state, action) => {
        state.loading = false
        state.medicaments.push(...action.payload)
      })
      .addCase(createMedicamentSlice.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Get All
      .addCase(getAllMedicamentSlices.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getAllMedicamentSlices.fulfilled, (state, action) => {
        state.loading = false
        state.medicaments = action.payload
      })
      .addCase(getAllMedicamentSlices.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Update
      .addCase(updateMedicamentSlice.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateMedicamentSlice.fulfilled, (state, action) => {
        state.loading = false
        const index = state.medicaments.findIndex(am => am.id === action.payload.id)
        if (index !== -1) {
          state.medicaments[index] = action.payload
        }
      })
      .addCase(updateMedicamentSlice.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Delete
      .addCase(deleteMedicamentSlice.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteMedicamentSlice.fulfilled, (state, action) => {
        state.loading = false
        state.medicaments = state.medicaments.filter(am => am.id !== Number(action.payload))
      })
      .addCase(deleteMedicamentSlice.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  }
})

export const {
  setSelectedMedicamentSlice,
  setForce,
  setPosologie,
  setDuree,
  setTypeConsommation,
  setFrequence,
  setQuantite,
  setCalendrier,
  setRemarks,
  resetMedicamentState,
  clearSelectedMedicamentSlice
} = medicamentSlice.actions
export default medicamentSlice.reducer
