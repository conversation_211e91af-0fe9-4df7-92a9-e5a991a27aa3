import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { AffectationMedical } from '@/domain/models'
import { 
  CreateAffectationMedicaleUseCase,
  GetAffectationMedicaleUseCase,
  UpdateAffectationMedicaleUseCase,
  DeleteAffectationMedicaleUseCase
} from '@/domain/usecases/professional/affectationMedicale'
import { CreateAffectationMedicaleRepository, DeleteAffectationMedicaleRepository, GetAffectationMedicaleRepository, UpdateAffectationMedicaleRepository } from '@/infrastructure/repositories/affectationMedicale'

interface AffectationMedicaleSliceState {
  affectationMedicales: AffectationMedical[]
  selectedAffectationMedicale: AffectationMedical | null
  affectationMedicaleState: {
    dateAcquisition: { [key: string]: string | null };
    remarks: { [key: string]: string },
  },
  loading: boolean
  error: string | null
}

const DEFAULT_AFFECTATION_MEDICALE_STATE = {
  dateAcquisition: {},
  remarks: {},
}

const initialState: AffectationMedicaleSliceState = {
  affectationMedicales: [],
  selectedAffectationMedicale: null,
  affectationMedicaleState: DEFAULT_AFFECTATION_MEDICALE_STATE,
  loading: false,
  error: null
}

export const createAffectationMedicale = createAsyncThunk(
  'affectationMedicale/create',
  async (data: Omit<AffectationMedical, "id">[], { rejectWithValue }) => {
    try {
      const createAffectationMedicaleRepository = new CreateAffectationMedicaleRepository()
      const createAffectationMedicaleUseCase = new CreateAffectationMedicaleUseCase(createAffectationMedicaleRepository)
      const result = await createAffectationMedicaleUseCase.execute(data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const getAffectationMedicales = createAsyncThunk(
  'affectationMedicale/getAll',
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getAffectationMedicaleRepository = new GetAffectationMedicaleRepository()
      const getAffectationMedicaleUseCase = new GetAffectationMedicaleUseCase(getAffectationMedicaleRepository)
      const result = await getAffectationMedicaleUseCase.getAll(carnetId)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const updateAffectationMedicale = createAsyncThunk(
  'affectationMedicale/update',
  async ({ id, data }: { id: number; data: Partial<AffectationMedical> }, { rejectWithValue }) => {
    try {
      const updateAffectationMedicaleRepository = new UpdateAffectationMedicaleRepository()
      const updateAffectationMedicaleUseCase = new UpdateAffectationMedicaleUseCase(updateAffectationMedicaleRepository)
      const result = await updateAffectationMedicaleUseCase.execute(id, data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const deleteAffectationMedicale = createAsyncThunk(
  'affectationMedicale/delete',
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteAffectationMedicaleRepository = new DeleteAffectationMedicaleRepository()
      const deleteAffectationMedicaleUseCase = new DeleteAffectationMedicaleUseCase(deleteAffectationMedicaleRepository)
      await deleteAffectationMedicaleUseCase.execute(id)
      return id
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

const affectationMedicaleSlice = createSlice({
  name: 'affectationMedicale',
  initialState,
  reducers: {
    setSelectedAffectationMedicale: (state, action) => {
      state.selectedAffectationMedicale = action.payload
    },
    setDateAcquisition: (state, action: PayloadAction<{item: string, value: string | null}>) => {
      state.affectationMedicaleState.dateAcquisition = {
        ...state.affectationMedicaleState.dateAcquisition,
        [action.payload.item]: action.payload.value,
      }
    },
    setRemarks: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.affectationMedicaleState.remarks = {
        ...state.affectationMedicaleState.remarks,
        [action.payload.item]: action.payload.value,
      }
    },
    resetAffectationMedicaleState: (state) => {
      state.affectationMedicaleState = DEFAULT_AFFECTATION_MEDICALE_STATE
    },
    clearSelectedAffectationMedicale: (state) => {
      state.selectedAffectationMedicale = null
    }
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createAffectationMedicale.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createAffectationMedicale.fulfilled, (state, action) => {
        state.loading = false
        state.affectationMedicales.push(...action.payload)
      })
      .addCase(createAffectationMedicale.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Get All
      .addCase(getAffectationMedicales.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getAffectationMedicales.fulfilled, (state, action) => {
        state.loading = false
        state.affectationMedicales = action.payload
      })
      .addCase(getAffectationMedicales.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Update
      .addCase(updateAffectationMedicale.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateAffectationMedicale.fulfilled, (state, action) => {
        state.loading = false
        const index = state.affectationMedicales.findIndex(am => am.id === action.payload.id)
        if (index !== -1) {
          state.affectationMedicales[index] = action.payload
        }
      })
      .addCase(updateAffectationMedicale.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Delete
      .addCase(deleteAffectationMedicale.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteAffectationMedicale.fulfilled, (state, action) => {
        state.loading = false
        state.affectationMedicales = state.affectationMedicales.filter(am => am.id !== Number(action.payload))
      })
      .addCase(deleteAffectationMedicale.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  }
})

export const {
  setSelectedAffectationMedicale,
  setDateAcquisition,
  setRemarks,
  resetAffectationMedicaleState,
  clearSelectedAffectationMedicale
} = affectationMedicaleSlice.actions
export default affectationMedicaleSlice.reducer
