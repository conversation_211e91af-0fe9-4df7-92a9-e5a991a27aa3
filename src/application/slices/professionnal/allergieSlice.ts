import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { Allergie } from '@/domain/models'

import { CreateAllergieRepository, DeleteAllergieRepository, GetAllAllergieRepository, UpdateAllergieRepository } from '@/infrastructure/repositories/allergie'
import { CreateAllergieUseCase, DeleteAllergieUseCase, GetAllAllergieUseCase, UpdateAllergieUseCase } from '@/domain/usecases/professional/allergie'

interface AllergieSliceState {
  allergies: Allergie[]
  selectedAllergieSlice: Allergie | null
  allergieState: {
    selectedReactions: {
      [key: string]: {
        [key: string]: boolean;
      };
    },
    remarks: { [key: string]: string },
  }
  loading: boolean
  error: string | null
}

const DEFAULT_ALLERGIE_STATE = {
  selectedReactions: {},
  remarks: {},
}

const initialState: AllergieSliceState = {
  allergies: [],
  selectedAllergieSlice: null,
  allergieState: DEFAULT_ALLERGIE_STATE,
  loading: false,
  error: null
}

export const createAllergieSlice = createAsyncThunk(
  'allergie/create',
  async (data: Omit<Allergie, "id">[], { rejectWithValue }) => {
    try {
      const createAllergieRepository = new CreateAllergieRepository()
      const createAllergieUseCase = new CreateAllergieUseCase(createAllergieRepository)
      const result = await createAllergieUseCase.execute(data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const getAllAllergieSlices = createAsyncThunk(
  'allergie/getAll',
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getAllAllergieRepository = new GetAllAllergieRepository()
      const getAllAllergieUseCase = new GetAllAllergieUseCase(getAllAllergieRepository)
      const result = await getAllAllergieUseCase.getAll(carnetId)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const updateAllergieSlice = createAsyncThunk(
  'allergie/update',
  async ({ id, data }: { id: number; data: Partial<Allergie> }, { rejectWithValue }) => {
    try {
      const updateAllergieRepository = new UpdateAllergieRepository()
      const updateAllergieUseCase = new UpdateAllergieUseCase(updateAllergieRepository)
      const result = await updateAllergieUseCase.execute(id, data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const deleteAllergieSlice = createAsyncThunk(
  'allergie/delete',
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteAllergieRepository = new DeleteAllergieRepository()
      const deleteAllergieUseCase = new DeleteAllergieUseCase(deleteAllergieRepository)
      await deleteAllergieUseCase.execute(id)
      return id
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

const allergieSlice = createSlice({
  name: 'allergie',
  initialState,
  reducers: {
    setSelectedAllergieSlice: (state, action) => {
      state.selectedAllergieSlice = action.payload
    },
    setSelectedReactions: (state, action: PayloadAction<{item: string, reaction: string, checked: boolean}>) => {
      state.allergieState.selectedReactions = {
        ...state.allergieState.selectedReactions,
        [action.payload.item]: { ...state.allergieState.selectedReactions[action.payload.item], [action.payload.reaction]: action.payload.checked },
      }
    },
    setRemarks: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.allergieState.remarks = {
        ...state.allergieState.remarks,
        [action.payload.item]: action.payload.value,
      }
    },
    resetAllergieState: (state) => {
      state.allergieState = DEFAULT_ALLERGIE_STATE
    },
    clearSelectedAllergieSlice: (state) => {
      state.selectedAllergieSlice = null
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createAllergieSlice.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createAllergieSlice.fulfilled, (state, action) => {
        state.loading = false
        state.allergies.push(...action.payload)
      })
      .addCase(createAllergieSlice.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Get All
      .addCase(getAllAllergieSlices.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getAllAllergieSlices.fulfilled, (state, action) => {
        state.loading = false
        state.allergies = action.payload
      })
      .addCase(getAllAllergieSlices.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Update
      .addCase(updateAllergieSlice.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateAllergieSlice.fulfilled, (state, action) => {
        state.loading = false
        const index = state.allergies.findIndex(am => am.id === action.payload.id)
        if (index !== -1) {
          state.allergies[index] = action.payload
        }
      })
      .addCase(updateAllergieSlice.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Delete
      .addCase(deleteAllergieSlice.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteAllergieSlice.fulfilled, (state, action) => {
        state.loading = false
        state.allergies = state.allergies.filter(am => am.id !== Number(action.payload))
      })
      .addCase(deleteAllergieSlice.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  }
})

export const {
  setSelectedAllergieSlice,
  setSelectedReactions,
  setRemarks,
  resetAllergieState,
  clearSelectedAllergieSlice,
} = allergieSlice.actions
export default allergieSlice.reducer
