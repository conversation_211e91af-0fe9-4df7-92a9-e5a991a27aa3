import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { consultation_medical } from '@/domain/models';
import {
  GetMedicalConsultationRepository,
  GetMedicalConsultationsByProfessionalIdRepository,
  GetMedicalConsultationsByPatientIdRepository,
  CreateMedicalConsultationRepository,
  UpdateMedicalConsultationRepository,
  DeleteMedicalConsultationRepository
} from '@/infrastructure/repositories/medicalConsultation';
import { GetMedicalConsultationUsecase } from '@/domain/usecases/professional/medicalConsultation/GetMedicalConsultationRepositoryUsecase';
import { GetMedicalConsultationsByProfessionalIdUsecase } from '@/domain/usecases/professional/medicalConsultation/GetConsultationsByProfessionalIdUsecase';
import { GetMedicalConsultationsByPatientIdUsecase } from '@/domain/usecases/professional/medicalConsultation/GetMedicalConsultationsByPatientIdUsecase';
import { CreateMedicalConsultationUsecase } from '@/domain/usecases/professional/medicalConsultation/CreateMedicalConsultationUsecase';
import { UpdateMedicalConsultationUsecase } from '@/domain/usecases/professional/medicalConsultation/UpdateMedicalConsultationUsecase';
import { DeleteMedicalConsultationUsecase } from '@/domain/usecases/professional/medicalConsultation/DeleteMedicalConsultationUsecase';
import { MedicalConsultationDTO } from '@/domain/DTOS';

interface ConsultationState {
  consultations: consultation_medical[];
  currentConsultation: consultation_medical | null;
  consultationsProfessional: MedicalConsultationDTO[] | null;
  loading: boolean;
  error: string | null;
}

const initialState: ConsultationState = {
  consultations: [],
  consultationsProfessional: [],
  currentConsultation: null,
  loading: false,
  error: null,
};

export const fetchMedicalConsultationById = createAsyncThunk(
  'medicalConsultation/fetchById',
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      const getMedicalConsultationRepository = new GetMedicalConsultationRepository();
      const getMedicalConsultationUsecase = new GetMedicalConsultationUsecase(getMedicalConsultationRepository);
      return await getMedicalConsultationUsecase.execute(id);
    } catch (error) {
      return rejectWithValue(error.message)
    }
  }
);

export const fetchMedicalConsultationsByProfessionalId = createAsyncThunk(
  'medicalConsultation/fetchByProfessionalId',
  async (professionalId: number, { rejectWithValue, dispatch }) => {
    try {
      const getMedicalConsultationsByProfessionalIdRepository = new GetMedicalConsultationsByProfessionalIdRepository();
      const getMedicalConsultationsByProfessionalIdUsecase = new GetMedicalConsultationsByProfessionalIdUsecase(getMedicalConsultationsByProfessionalIdRepository);
      return await getMedicalConsultationsByProfessionalIdUsecase.execute(professionalId);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchMedicalConsultationsByPatientId = createAsyncThunk(
  'medicalConsultation/fetchByPatientId',
  async (patientId: number, { rejectWithValue, dispatch }) => {
    try {
      const getMedicalConsultationsByPatientIdRepository = new GetMedicalConsultationsByPatientIdRepository();
      const getMedicalConsultationsByPatientIdUsecase = new GetMedicalConsultationsByPatientIdUsecase(getMedicalConsultationsByPatientIdRepository);
      return await getMedicalConsultationsByPatientIdUsecase.execute(patientId);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const createMedicalConsultation = createAsyncThunk(
  'medicalConsultation/create',
  async (consultation: Omit<consultation_medical, 'id'>, { rejectWithValue, dispatch }) => {
    try {
      const createMedicalConsultationRepository = new CreateMedicalConsultationRepository();
      const createMedicalConsultationUsecase = new CreateMedicalConsultationUsecase(createMedicalConsultationRepository);
      return await createMedicalConsultationUsecase.execute(consultation);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateMedicalConsultation = createAsyncThunk(
  'medicalConsultation/update',
  async ({
    id,
    consultation,
  }: {
    id: number;
    consultation: Partial<consultation_medical>;
  }, { rejectWithValue, dispatch }) => {
    try {
      const updateMedicalConsultationRepository = new UpdateMedicalConsultationRepository();
      const updateMedicalConsultationUsecase = new UpdateMedicalConsultationUsecase(updateMedicalConsultationRepository);
      return await updateMedicalConsultationUsecase.execute(id, consultation);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const deleteMedicalConsultation = createAsyncThunk(
  'medicalConsultation/delete',
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      const deleteMedicalConsultationRepository = new DeleteMedicalConsultationRepository();
      const deleteMedicalConsultationUsecase = new DeleteMedicalConsultationUsecase(deleteMedicalConsultationRepository);
      return await deleteMedicalConsultationUsecase.execute(id);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const medicalConsultationSlice = createSlice({
  name: 'medicalConsultation',
  initialState,
  reducers: {
    clearCurrentMedicalConsultation: (state) => {
      state.currentConsultation = null;
    },
    setSelectedConsultation: (state, action) => {
      state.currentConsultation = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch consultation by ID
    builder
      .addCase(fetchMedicalConsultationById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMedicalConsultationById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentConsultation = action.payload;
      })
      .addCase(fetchMedicalConsultationById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch consultation';
      })

    // Fetch consultations by professional ID
    builder
      .addCase(fetchMedicalConsultationsByProfessionalId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMedicalConsultationsByProfessionalId.fulfilled, (state, action) => {
        state.loading = false;
        state.consultationsProfessional = action.payload;
      })
      .addCase(fetchMedicalConsultationsByProfessionalId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch consultations';
      })

    // Fetch consultations by patient ID
    builder
      .addCase(fetchMedicalConsultationsByPatientId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMedicalConsultationsByPatientId.fulfilled, (state, action) => {
        state.loading = false;
        state.consultations = action.payload;
      })
      .addCase(fetchMedicalConsultationsByPatientId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch consultations by patient';
      })

    // Create medical consultation
    builder
      .addCase(createMedicalConsultation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createMedicalConsultation.fulfilled, (state, action) => {
        state.loading = false;
        state.currentConsultation = action.payload;
        if (action.payload) {
          state.consultations.push(action.payload);
        }
      })
      .addCase(createMedicalConsultation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create consultation';
      })

    // Update medical consultation
    builder
      .addCase(updateMedicalConsultation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateMedicalConsultation.fulfilled, (state, action) => {
        state.loading = false;
        state.currentConsultation = action.payload;
        if (action.payload) {
          const index = state.consultations.findIndex(c => c.id === action.payload.id);
          if (index !== -1) {
            state.consultations[index] = action.payload;
          }
        }
      })
      .addCase(updateMedicalConsultation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update consultation';
      })

    // Delete medical consultation
    builder
      .addCase(deleteMedicalConsultation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteMedicalConsultation.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          state.consultations = state.consultations.filter(c => c.id !== action.payload.id);
          if (state.currentConsultation?.id === action.payload.id) {
            state.currentConsultation = null;
          }
        }
      })
      .addCase(deleteMedicalConsultation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to delete consultation';
      });
  },
});

export const { clearCurrentMedicalConsultation, setSelectedConsultation, clearError } = medicalConsultationSlice.actions;
export default medicalConsultationSlice.reducer;