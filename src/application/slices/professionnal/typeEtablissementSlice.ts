import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit'
import { ListeTypeEtablissement } from '@/domain/models'
import { typeEtablissementRepository } from '@/infrastructure/repositories'

interface TypeEtablissementSlice {
  listes: ListeTypeEtablissement[]
  loading: boolean
  error: string | null
  isPopoverOpen: boolean
}

const initialState: TypeEtablissementSlice = {
  listes: [],
  loading: false,
  error: null,
  isPopoverOpen: false
}

export const getTypeEtablissements = createAsyncThunk(
  'typeEtablissement/getTypeEtablissements',
  async (_, { rejectWithValue }) => {
    try {
      const data = await typeEtablissementRepository.getTypeEtablissements()

      return data
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch typeEtablissement')
    }
  }
)

const typeEtablissementSlice = createSlice({
  name: 'typeEtablissement',
  initialState,
  reducers: {
    setIsPopOverOpen(state, action: PayloadAction<boolean>) {
      state.isPopoverOpen = action.payload
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getTypeEtablissements.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getTypeEtablissements.fulfilled, (state, action) => {
        state.loading = false
        state.listes = action.payload
      })
      .addCase(getTypeEtablissements.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  }
})

export const { setIsPopOverOpen } = typeEtablissementSlice.actions

export default typeEtablissementSlice.reducer
