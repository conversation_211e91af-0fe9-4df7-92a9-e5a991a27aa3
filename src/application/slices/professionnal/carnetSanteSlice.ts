import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { CarnetSante } from '@/domain/models'
import { CreateCarnetSanteRepository } from '@/infrastructure/repositories/carnetDeSante/CreateCarnetSanteRepository'
import { CreateCarnetSanteUseCase, DeleteCarnetSanteUseCase, GetCarnetSanteUseCase, GetIdCarnetSanteUseCase, UpdateCarnetSanteUseCase } from '@/domain/usecases/professional/carnetSante'
import { GetCarnetSanteRepository } from '@/infrastructure/repositories/carnetDeSante/GetCarnetSanteRepository'
import { UpdateCarnetSanteRepository } from '@/infrastructure/repositories/carnetDeSante/UpdateCarnetSanteRepository'
import { DeleteCarnetSanteRepository } from '@/infrastructure/repositories/carnetDeSante/DeleteCarnetSanteRepository'
import { CarnetSanteDTO } from '@/domain/DTOS'
import { GetIdCarnetSanteRepository } from '@/infrastructure/repositories/carnetDeSante/GetIdCarnetSanteRepository'

interface CarnetSanteSliceState {
  carnetSante: CarnetSante | null
  carnetSantes: CarnetSanteDTO | null
  selectedCarnetSante: CarnetSante | null
  selectedSearch: string[],
  idCarnetSante: number | null
  loading: boolean
  error: string | null
}

const initialState: CarnetSanteSliceState = {
  carnetSante: null,
  carnetSantes: null,
  selectedCarnetSante: null,
  selectedSearch: [],
  idCarnetSante: null,
  loading: false,
  error: null
}

export const createCarnetSante = createAsyncThunk(
  'carnetSante/create',
  async (data: Omit<CarnetSante, "id">, { rejectWithValue }) => {
    try {
      const createCarnetSanteRepository = new CreateCarnetSanteRepository()
      const createCarnetSanteUseCase = new CreateCarnetSanteUseCase(createCarnetSanteRepository)
      const result = await createCarnetSanteUseCase.execute(data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const getCarnetSantes = createAsyncThunk(
  'carnetSante/get',
  async (id: number, { rejectWithValue }) => {
    try {
      const getCarnetSanteRepository = new GetCarnetSanteRepository()
      const getCarnetSanteUseCase = new GetCarnetSanteUseCase(getCarnetSanteRepository)
      const result = await getCarnetSanteUseCase.execute(id)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const getIdCarnetSante = createAsyncThunk(
  'carnetSante/getId',
  async (id: number, { rejectWithValue }) => {
    try {
      const getIdCarnetSanteRepository = new GetIdCarnetSanteRepository()
      const getIdCarnetSanteUseCase = new GetIdCarnetSanteUseCase(getIdCarnetSanteRepository)
      const result = await getIdCarnetSanteUseCase.execute(id)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const updateCarnetSante = createAsyncThunk(
  'carnetSante/update',
  async ({ id, data }: { id: number; data: Partial<CarnetSante> }, { rejectWithValue }) => {
    try {
      const updateCarnetSanteRepository = new UpdateCarnetSanteRepository()
      const updateCarnetSanteUseCase = new UpdateCarnetSanteUseCase(updateCarnetSanteRepository)
      const result = await updateCarnetSanteUseCase.execute(id, data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const deleteCarnetSante = createAsyncThunk(
  'carnetSante/delete',
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteCarnetSanteRepository = new DeleteCarnetSanteRepository()
      const deleteCarnetSanteUseCase = new DeleteCarnetSanteUseCase(deleteCarnetSanteRepository)
      await deleteCarnetSanteUseCase.execute(id)
      return id
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

const carnetSanteSlice = createSlice({
  name: 'carnetSante',
  initialState,
  reducers: {
    setSelectedCarnetSante(state, action) {
      state.selectedCarnetSante = action.payload
    },
    setSelectedSearch(state, action: PayloadAction<{value: string}>) {
      state.selectedSearch.push(action.payload.value);
    },
    onDeleteSelectedSearch(state, action: PayloadAction<{itemToDelete: string}>) {
      state.selectedSearch = state.selectedSearch.filter(item => item !== action.payload.itemToDelete);
    },
    resetSearchState(state) {
      state.selectedSearch = []
    },
    clearSelectedCarnetSante(state) {
      state.selectedCarnetSante = null
    },
    resetId(state) {
      state.idCarnetSante = null
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(createCarnetSante.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createCarnetSante.fulfilled, (state, action) => {
        state.loading = false
        state.carnetSante = action.payload
      })
      .addCase(createCarnetSante.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      .addCase(getCarnetSantes.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getCarnetSantes.fulfilled, (state, action) => {
        state.loading = false
        state.carnetSantes = action.payload
      })
      .addCase(getCarnetSantes.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      .addCase(getIdCarnetSante.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getIdCarnetSante.fulfilled, (state, action) => {
        state.loading = false
        state.idCarnetSante = action.payload
      })
      .addCase(getIdCarnetSante.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      .addCase(updateCarnetSante.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateCarnetSante.fulfilled, (state, action) => {
        state.loading = false
        state.carnetSante = action.payload
      })
      .addCase(updateCarnetSante.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      .addCase(deleteCarnetSante.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteCarnetSante.fulfilled, (state, action) => {
        state.loading = false
        state.carnetSantes = null
      })
      .addCase(deleteCarnetSante.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  }
})

export const {
  setSelectedCarnetSante,
  setSelectedSearch,
  onDeleteSelectedSearch,
  resetSearchState,
  clearSelectedCarnetSante,
  resetId 
} = carnetSanteSlice.actions
export default carnetSanteSlice.reducer
