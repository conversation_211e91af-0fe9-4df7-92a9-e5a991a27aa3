import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { HistoriqueCarnetSante } from '@/domain/models'
import { CreateHistoriqueCarnetSanteRepository } from '@/infrastructure/repositories/historiqueCarnetSante/CreateHistoriqueCarnetSanteRepository'
import { CreatehistoriqueCarnetSanteUseCase, DeleteHistoriqueCarnetSanteUseCase, GetHistoriqueCarnetSanteUseCase, UpdateHistoriqueCarnetSanteUseCase } from '@/domain/usecases/professional/historiqueCarnetSante'
import { DeleteHistoriqueCarnetSanteRepository } from '@/infrastructure/repositories/historiqueCarnetSante/DeleteHistoriqueCarnetSanteRepository'
import { UpdateHistoriqueCarnetSanteRepository } from '@/infrastructure/repositories/historiqueCarnetSante/UpdateHistoriqueCarnetSanteRepository'
import { GetHistoriqueCarnetSanteRepository } from '@/infrastructure/repositories/historiqueCarnetSante'
import { HistoriqueCarnetSanteDTO } from '@/domain/DTOS/HistoriqueCarnetSanteDTO'

interface HistoriqueCarnetSanteSliceState {
  historiqueCarnetSantes: HistoriqueCarnetSanteDTO[]
  selectedHistoriqueCarnetSante: HistoriqueCarnetSante | null
  loading: boolean
  error: string | null
}

const initialState: HistoriqueCarnetSanteSliceState = {
  historiqueCarnetSantes: [],
  selectedHistoriqueCarnetSante: null,
  loading: false,
  error: null
}

export const createHistoriqueCarnetSante = createAsyncThunk(
  'historiqueCarnetSante/create',
  async (data: Omit<HistoriqueCarnetSante, "id">[], { rejectWithValue }) => {
    try {
      const createHistoriqueCarnetSanteRepository = new CreateHistoriqueCarnetSanteRepository()
      const createhistoriqueCarnetSanteUseCase = new CreatehistoriqueCarnetSanteUseCase(createHistoriqueCarnetSanteRepository)
      const result = await createhistoriqueCarnetSanteUseCase.execute(data)
      console.log(result);
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const getHistoriqueCarnetSantes = createAsyncThunk(
  'historiqueCarnetSante/get',
  async ({ carnetId, tableConcernee }: { carnetId: number, tableConcernee: string }, { rejectWithValue }) => {
    try {
      const getHistoriqueCarnetSanteRepository = new GetHistoriqueCarnetSanteRepository()
      const getHistoriqueCarnetSanteUseCase = new GetHistoriqueCarnetSanteUseCase(getHistoriqueCarnetSanteRepository)
      const result = await getHistoriqueCarnetSanteUseCase.execute(carnetId, tableConcernee)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const updateHistoriqueCarnetSante = createAsyncThunk(
  'historiqueCarnetSante/update',
  async ({ id, data }: { id: number; data: Partial<HistoriqueCarnetSante> }, { rejectWithValue }) => {
    try {
      const updateHistoriqueCarnetSanteRepository = new UpdateHistoriqueCarnetSanteRepository()
      const updateHistoriqueCarnetSanteUseCase = new UpdateHistoriqueCarnetSanteUseCase(updateHistoriqueCarnetSanteRepository)
      const result = await updateHistoriqueCarnetSanteUseCase.execute(id, data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const deleteHistoriqueCarnetSante = createAsyncThunk(
  'historiqueCarnetSante/delete',
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteHistoriqueCarnetSanteRepository = new DeleteHistoriqueCarnetSanteRepository()
      const deleteHistoriqueCarnetSanteUseCase = new DeleteHistoriqueCarnetSanteUseCase(deleteHistoriqueCarnetSanteRepository)
      await deleteHistoriqueCarnetSanteUseCase.execute(id)
      return id
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

const historiqueCarnetSanteSlice = createSlice({
  name: 'historiqueCarnetSante',
  initialState,
  reducers: {
    setSelectedHistoriqueCarnetSante(state, action) {
      state.selectedHistoriqueCarnetSante = action.payload
    },
    clearSelectedHistoriqueCarnetSante(state) {
      state.historiqueCarnetSantes = []
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getHistoriqueCarnetSantes.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getHistoriqueCarnetSantes.fulfilled, (state, action) => {
        state.loading = false
        state.historiqueCarnetSantes = action.payload
      })
      .addCase(getHistoriqueCarnetSantes.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      .addCase(updateHistoriqueCarnetSante.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateHistoriqueCarnetSante.fulfilled, (state, action) => {
        state.loading = false
        state.selectedHistoriqueCarnetSante = action.payload
      })
      .addCase(updateHistoriqueCarnetSante.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      .addCase(deleteHistoriqueCarnetSante.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteHistoriqueCarnetSante.fulfilled, (state, action) => {
        state.loading = false
        state.historiqueCarnetSantes = state.historiqueCarnetSantes.filter(item => item.id !== action.payload)
      })
      .addCase(deleteHistoriqueCarnetSante.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  }
})

export const { setSelectedHistoriqueCarnetSante, clearSelectedHistoriqueCarnetSante } = historiqueCarnetSanteSlice.actions
export default historiqueCarnetSanteSlice.reducer
