import { EtablissementProfessionnel } from "@/domain/models";
import GetEtablishmentProfessionalUsecase from "@/domain/usecases/professional/GetEtablishmentProfessional/GetEtablishmentProfessionalUsecase";
import GetEtablissementProfessionnelRepository from "@/infrastructure/repositories/EtablissementProfessionnel/GetEtablissementProfessionnelRepository";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ProfessionalEtablishmentSlice {
  listes: EtablissementProfessionnel[];
  loading: boolean;
  error: string | null;
}

const initialState: ProfessionalEtablishmentSlice = {
  listes: [],
  loading: false,
  error: null,
};

export const getProfessionalEtablishments = createAsyncThunk(
  "professionnalEtablishment/getProfessionalEtablishments",
  async (_, { rejectWithValue }) => {
    try {
      const getProfessionalEtablishments =
        new GetEtablissementProfessionnelRepository();
      const getProfessionalEtablishmentsUsecase =
        new GetEtablishmentProfessionalUsecase(getProfessionalEtablishments);

      return getProfessionalEtablishmentsUsecase.execute();
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

const professionalEtablishmentSlice = createSlice({
  name: "professionnalEtablishment",
  initialState,
  reducers: {
    setIsLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getProfessionalEtablishments.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProfessionalEtablishments.fulfilled, (state, action) => {
        state.loading = false;
        state.listes = action.payload;
      })
      .addCase(getProfessionalEtablishments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setIsLoading, setError } = professionalEtablishmentSlice.actions;
export default professionalEtablishmentSlice.reducer;

