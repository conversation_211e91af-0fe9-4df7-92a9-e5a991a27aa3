import { ProfessionalCardDTO, TimeSlotProffessionalCard } from "@/domain/DTOS";
import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { RendezVous } from "@/domain/models";
import CancelAppointmentUsecase from "@/domain/usecases/appointment/CancelAppointmentUsecase";
import CreateAppointmentUsecase from "@/domain/usecases/appointment/CreateAppointmentUsecase";
import DeleteAppointmentUsecase from "@/domain/usecases/appointment/DeleteAppointmentUsecase";
import GetAppointmentByIdUsecase from "@/domain/usecases/appointment/GetAppointmentByIdUsecase";
import {
  CancelAppointmentByProfessionalUsecase,
  CompleteAppointmentUsecase,
  GetAppointmentListByPatientIdUsecase,
  GetAppointmentListByProfessionalIdUsecase,
} from "@/domain/usecases/appointment";
import MarkAppointmentAsDoneUsecase from "@/domain/usecases/appointment/MarkAppointmentAsDoneUsecase";
import UpdateAppointmentUsecase from "@/domain/usecases/appointment/UpdateAppointmentUsecase";
import CancelAppointmentRepository from "@/infrastructure/repositories/appointment/CancelAppointmentRepository";
import CreateAppointmentRepository from "@/infrastructure/repositories/appointment/CreateAppointmentRepository";
import DeleteAppointmentRepository from "@/infrastructure/repositories/appointment/DeleteAppointmentRepository";
import GetAppointmentByIdRepository from "@/infrastructure/repositories/appointment/GetAppointmentByIdRepository";
import {
  GetAppointmentListByPatientIdRepository,
  GetAppointmentListByProfessionalIdRepository,
  PostponeAppointmentRepository,
} from "@/infrastructure/repositories/appointment";
import MarkAppointmentAsDoneRepository from "@/infrastructure/repositories/appointment/MarkAppointmentAsDoneRepository";
import UpdateAppointmentRepository from "@/infrastructure/repositories/appointment/UpdateAppointmentRepository";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { CancelAppointmentByProfessionalRepository } from "@/infrastructure/repositories/appointment/CancelAppointmentByProfessionalRepository";
import { AnnulerRendezVous } from "@/domain/models/AnnulerRendezVous";
import { CompleteAppointmentRepository } from "@/infrastructure/repositories/appointment/CompleteAppointmentRepository";
import { PostponeAppointmentUsecase } from "@/domain/usecases/appointment/PostponeAppointmentUsecase";
import { addNotification } from "../notification/notificationSlice";
import { AppointmentSuccesMessages } from "@/domain/usecases/appointment/messages/AppointmentSuccesMessage";
import { AppointmentErrorsMessages } from "@/domain/usecases/appointment/messages/AppointmentErrorsMessages";
import GetUserByIdRepository from "@/infrastructure/repositories/user/GetUserByIdRepository";
import GetPatientByIdRepository from "@/infrastructure/repositories/patients/GetPatientByIdRepository";
import { GetProfessionalByIdRepository } from "@/infrastructure/repositories/professionals/GetProfessionalByIdRepository";
import { GetUserByIdUsecase } from "@/domain/usecases/user";
import GetPatientByIdUsecase from "@/domain/usecases/patients/GetPatientByIdUsecase";
import { GetProfessionalByIdUsecase } from "@/domain/usecases/professional/GetProfessionals/GetProfessionalByIdUsecase";
import CancelAppointmentEmailService from "@/services/customEmailService/CancelAppointmentEmailService";
import ConfirmAppointmentEmailService from "@/services/customEmailService/ConfirmAppointmentEmailService";

// Services
const confirmAppointmentEmailService = new ConfirmAppointmentEmailService();

// Repository relatives aux rendez-vous
const cancelAppointmentRepository = new CancelAppointmentRepository();
const createAppointmentRepository = new CreateAppointmentRepository();
const deleteAppointmentRepository = new DeleteAppointmentRepository();
const getAppointmentByIdRepository = new GetAppointmentByIdRepository();
const getAppointmentListByProfessionalIdRepository =
  new GetAppointmentListByProfessionalIdRepository();
const markAppointmentAsDoneRepository = new MarkAppointmentAsDoneRepository();
const updateAppointmentRepository = new UpdateAppointmentRepository();

// Professionel & Patient Repository
const getUserByIdRepository = new GetUserByIdRepository();
const getPatientByIdRepository = new GetPatientByIdRepository();
const getProfessionalByIdRepository = new GetProfessionalByIdRepository();

// Professionel & Patient Usecase
const getUserByIdUsecase = new GetUserByIdUsecase(getUserByIdRepository);
const getProfessionalByIdUsecase = new GetProfessionalByIdUsecase(
  getProfessionalByIdRepository,
);
const getPatientByIdUsecase = new GetPatientByIdUsecase(
  getPatientByIdRepository,
);

// Autres
const cancelAppointmentEmailService = new CancelAppointmentEmailService();

// Usecase relatives aux rendez-vous
const cancelAppointmentUsecase = new CancelAppointmentUsecase(
  cancelAppointmentRepository,
  getUserByIdUsecase,
  getPatientByIdUsecase,
  getProfessionalByIdUsecase,
  cancelAppointmentEmailService,
);
const createAppointmentUsecase = new CreateAppointmentUsecase(
  createAppointmentRepository,
  confirmAppointmentEmailService,
  getPatientByIdUsecase,
  getUserByIdUsecase,
  getProfessionalByIdUsecase,
);
const deleteAppointmentUsecase = new DeleteAppointmentUsecase(
  deleteAppointmentRepository,
);
const getAppointmentByIdUsecase = new GetAppointmentByIdUsecase(
  getAppointmentByIdRepository,
);
const getAppointmentListByProfessionalIdUsecase =
  new GetAppointmentListByProfessionalIdUsecase(
    getAppointmentListByProfessionalIdRepository,
  );
const markAppointmentAsDoneUsecase = new MarkAppointmentAsDoneUsecase(
  markAppointmentAsDoneRepository,
);
const updateAppointmentUsecase = new UpdateAppointmentUsecase(
  updateAppointmentRepository,
);

const getAppointmentListByPatientIdRepository =
  new GetAppointmentListByPatientIdRepository();

const getAppointmentListByPatientIdUsecase =
  new GetAppointmentListByPatientIdUsecase(
    getAppointmentListByPatientIdRepository,
  );

const cancelAppointmentByProfessionalRepository =
  new CancelAppointmentByProfessionalRepository();

const cancelAppointmentByProfessionalUsecase =
  new CancelAppointmentByProfessionalUsecase(
    cancelAppointmentByProfessionalRepository,
  );

const completeAppointmentRepository = new CompleteAppointmentRepository();

const completeAppointmentUsecase = new CompleteAppointmentUsecase(
  completeAppointmentRepository,
);

const postponeAppointmentRepository = new PostponeAppointmentRepository();

const postponeAppointmentUsecase = new PostponeAppointmentUsecase(
  postponeAppointmentRepository,
);

interface AppointmentSlice {
  appointments: RendezVous[];
  appointmentPatient: AppointmentPatientDTO[] | null;
  appointmentProfessional: AppointmentProfessionalDTO[] | null;
  selectedAppointment: RendezVous | null;
  professional: ProfessionalCardDTO | null;
  timeSlot: TimeSlotProffessionalCard;
  loading: boolean;
  error: string | null;
}

const initialState: AppointmentSlice = {
  appointments: [],
  appointmentPatient: [],
  appointmentProfessional: [],
  selectedAppointment: null,
  professional: null,
  timeSlot: null,
  loading: false,
  error: null,
};

export const createAppointment = createAsyncThunk(
  "appointments/create",
  async (appointment: Omit<RendezVous, "id">, { rejectWithValue }) => {
    try {
      const data = await createAppointmentUsecase.execute(appointment);

      return data;
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

export const getAppointmentListByProfessionalId = createAsyncThunk(
  "appointments/getByProfessionalId",
  async (professionalId: number, { rejectWithValue }) => {
    try {
      const data = await getAppointmentListByProfessionalIdUsecase.execute(
        professionalId,
      );
      return data;
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

export const getAppointmentListByPatientId = createAsyncThunk(
  "appointments/getByPatientId",
  async (patientId: number, { rejectWithValue }) => {
    try {
      const data = await getAppointmentListByPatientIdUsecase.execute(
        patientId,
      );
      return data;
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

export const getAppointmentById = createAsyncThunk(
  "appointments/getById",
  async (appointmentId: number, { rejectWithValue }) => {
    try {
      const data = await getAppointmentByIdUsecase.execute(appointmentId);
      return data;
    } catch (error) {
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

export const cancelAppointment = createAsyncThunk(
  "appointments/cancel",
  async (
    appointment: AppointmentProfessionalDTO,
    { rejectWithValue, dispatch },
  ) => {
    try {
      const data = await cancelAppointmentUsecase.execute(appointment);

      dispatch(
        addNotification({
          message: AppointmentSuccesMessages.APPOINTMENT_UPDATED,
          type: "success",
        }),
      );
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: AppointmentErrorsMessages.APPOINTMENT_UPDATED,
          type: "error",
        }),
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

export const completeAppointment = createAsyncThunk(
  "appointments/complete",
  async (appointmentId: number, { rejectWithValue, dispatch }) => {
    try {
      const data = await completeAppointmentUsecase.execute(appointmentId);
      dispatch(
        addNotification({
          message: AppointmentSuccesMessages.APPOINTMENT_UPDATED,
          type: "success",
        }),
      );
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: AppointmentErrorsMessages.APPOINTMENT_UPDATED,
          type: "error",
        }),
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

export const cancelAppointmentByProfessional = createAsyncThunk(
  "appointments/cancelByProfessional",
  async (
    appointmentData: Omit<AnnulerRendezVous, "id">,
    { rejectWithValue, dispatch },
  ) => {
    try {
      const data = await cancelAppointmentByProfessionalUsecase.execute(
        appointmentData,
      );
      dispatch(
        addNotification({
          message: AppointmentSuccesMessages.APPOINTMENT_UPDATED,
          type: "success",
        }),
      );
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: AppointmentErrorsMessages.APPOINTMENT_UPDATED,
          type: "error",
        }),
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

export const postponeAppointment = createAsyncThunk(
  "appointments/postpone",
  async (
    { id, dateRendezVous }: { id: number; dateRendezVous: Date },
    { rejectWithValue, dispatch },
  ) => {
    try {
      const data = await postponeAppointmentUsecase.execute(
        id,
        dateRendezVous,
      );
      dispatch(
        addNotification({
          message: AppointmentSuccesMessages.APPOINTMENT_UPDATED,
          type: "success",
        }),
      );
      return data;
    } catch (error) {
      dispatch(
        addNotification({
          message: AppointmentErrorsMessages.APPOINTMENT_UPDATED,
          type: "error",
        }),
      );
      return rejectWithValue(error.message || ErrorMessages.UNKNOWN_ERROR);
    }
  },
);

const appointmentSlice = createSlice({
  name: "appointments",
  initialState,
  reducers: {
    resetData: (state) => {
      Object.assign(state, initialState);
    },
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    setProfessionnal: (state, action: PayloadAction<ProfessionalCardDTO>) => {
      state.professional = action.payload;
    },

    setTimeSlot: (state, action: PayloadAction<TimeSlotProffessionalCard>) => {
      state.timeSlot = action.payload;
    },
    resetTimeSlot: (state) => {
      state.timeSlot = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createAppointment.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createAppointment.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(createAppointment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getAppointmentListByProfessionalId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        getAppointmentListByProfessionalId.fulfilled,
        (state, action) => {
          state.loading = false;
          state.appointmentProfessional = action.payload;
        },
      )
      .addCase(getAppointmentListByProfessionalId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getAppointmentListByPatientId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        getAppointmentListByPatientId.fulfilled,
        (state, action) => {
          state.loading = false;
          state.appointmentPatient = action.payload;
        },
      )
      .addCase(getAppointmentListByPatientId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getAppointmentById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAppointmentById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedAppointment = action.payload;
      })
      .addCase(getAppointmentById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(cancelAppointment.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(cancelAppointment.fulfilled, (state, action) => {
        state.loading = false;
        if (state.appointmentPatient && action.payload) {
          const existingAppointment = state.appointmentPatient.find((apt) =>
            apt.id === action.payload.id
          );
          if (existingAppointment) {
            state.appointmentPatient = state.appointmentPatient.map((apt) =>
              apt.id === action.payload.id
                ? {
                  ...existingAppointment,
                  statut: action.payload.statut,
                }
                : apt
            );
          }
        }
        state.error = null;
      })
      .addCase(cancelAppointment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setIsLoading,
  resetData,
  setError,
  setProfessionnal,
  setTimeSlot,
  resetTimeSlot,
} = appointmentSlice.actions;
export default appointmentSlice.reducer;
