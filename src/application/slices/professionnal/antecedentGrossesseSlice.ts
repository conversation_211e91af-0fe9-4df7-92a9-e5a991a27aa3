import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { AntecedentGrossesse } from '@/domain/models'
import { 
  CreateAntecedentGrossesseUseCase,
  GetAllAntecedentGrossesseUseCase,
  GetByIdAntecedentGrossesseUseCase,
  UpdateAntecedentGrossesseUseCase,
  DeleteAntecedentGrossesseUseCase
} from '@/domain/usecases/professional/antecedentGrossesse'
import {
  CreateAntecedentGrossesseRepository,
  DeleteAntecedentGrossesseRepository,
  GetAllAntecedentGrossesseRepository,
  GetByIdAntecedentGrossesseRepository,
  UpdateAntecedentGrossesseRepository
} from '@/infrastructure/repositories/antecedentGrossesse'

interface AntecedentGrossesseSliceState {
  antecedentGrossesse: AntecedentGrossesse[]
  selectedAntecedentGrossesse: AntecedentGrossesse | null
  antecedentGrossesseState: {
    estEnceinte: { [key: string]: boolean },
    date: { [key: string]: string | null };
    parite: { [key: string]: string },
    nombreEnfants: { [key: string]: number },
    remarks: { [key: string]: string },
  },
  loading: boolean
  error: string | null
}

const DEFAULT_ANTECEDANT_GROSSESSE_STATE = {
  estEnceinte: {},
  date: {},
  parite: {},
  nombreEnfants: {},
  remarks: {},
}

const initialState: AntecedentGrossesseSliceState = {
  antecedentGrossesse: [],
  selectedAntecedentGrossesse: null,
  antecedentGrossesseState: DEFAULT_ANTECEDANT_GROSSESSE_STATE,
  loading: false,
  error: null
}

export const createAntecedentGrossesse = createAsyncThunk(
  'antecedentGrossesse/create',
  async (data: Omit<AntecedentGrossesse, "id">[], { rejectWithValue }) => {
    try {
      const createAntecedentGrossesseRepository = new CreateAntecedentGrossesseRepository()
      const createAntecedentGrossesseUseCase = new CreateAntecedentGrossesseUseCase(createAntecedentGrossesseRepository)
      const result = await createAntecedentGrossesseUseCase.execute(data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const getAllAntecedentGrossesse = createAsyncThunk(
  'antecedentGrossesse/getAll',
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getAllAntecedentGrossesseRepository = new GetAllAntecedentGrossesseRepository()
      const getAllAntecedentGrossesseUseCase = new GetAllAntecedentGrossesseUseCase(getAllAntecedentGrossesseRepository)
      const result = await getAllAntecedentGrossesseUseCase.execute(carnetId)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const getByIdAntecedentGrossesse = createAsyncThunk(
  'antecedentGrossesse/getById',
  async (carnetId: number, { rejectWithValue }) => {
    try {
      const getByIdAntecedentGrossesseRepository = new GetByIdAntecedentGrossesseRepository()
      const getByIdAntecedentGrossesseUseCase = new GetByIdAntecedentGrossesseUseCase(getByIdAntecedentGrossesseRepository)
      const result = await getByIdAntecedentGrossesseUseCase.execute(carnetId)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const updateAntecedentGrossesse = createAsyncThunk(
  'antecedentGrossesse/update',
  async ({ id, data }: { id: number; data: Partial<AntecedentGrossesse> }, { rejectWithValue }) => {
    try {
      const updateAntecedentGrossesseRepository = new UpdateAntecedentGrossesseRepository()
      const updateAntecedentGrossesseUseCase = new UpdateAntecedentGrossesseUseCase(updateAntecedentGrossesseRepository)
      const result = await updateAntecedentGrossesseUseCase.execute(id, data)
      return result
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

export const deleteAntecedentGrossesse = createAsyncThunk(
  'antecedentGrossesse/delete',
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteAntecedentGrossesseRepository = new DeleteAntecedentGrossesseRepository()
      const deleteAntecedentGrossesseUseCase = new DeleteAntecedentGrossesseUseCase(deleteAntecedentGrossesseRepository)
      await deleteAntecedentGrossesseUseCase.execute(id)
      return id
    } catch (error) {
      return rejectWithValue((error as Error).message)
    }
  }
)

const antecedentGrossesseSlice = createSlice({
  name: 'antecedentGrossesse',
  initialState,
  reducers: {
    setSelectedAntecedentGrossesse: (state, action) => {
      state.selectedAntecedentGrossesse = action.payload
    },
    setParite: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.antecedentGrossesseState.parite = {
        ...state.antecedentGrossesseState.parite,
        [action.payload.item]: action.payload.value,
      }
    },
    setEstEnceinte: (state, action: PayloadAction<{item: string, value: boolean}>) => {
      state.antecedentGrossesseState.estEnceinte = {
        ...state.antecedentGrossesseState.estEnceinte,
        [action.payload.item]: action.payload.value,
      }
    },
    setDate: (state, action: PayloadAction<{item: string, value: string | null}>) => {
      state.antecedentGrossesseState.date = {
        ...state.antecedentGrossesseState.date,
        [action.payload.item]: action.payload.value,
      }
    },
    setNombreEnfants: (state, action: PayloadAction<{item: string, value: number}>) => {
      state.antecedentGrossesseState.nombreEnfants = {
        ...state.antecedentGrossesseState.nombreEnfants,
        [action.payload.item]: action.payload.value,
      }
    },
    setRemarks: (state, action: PayloadAction<{item: string, value: string}>) => {
      state.antecedentGrossesseState.remarks = {
        ...state.antecedentGrossesseState.remarks,
        [action.payload.item]: action.payload.value,
      }
    },
    resetAntecedentGrossesseState: (state) => {
      state.antecedentGrossesseState = DEFAULT_ANTECEDANT_GROSSESSE_STATE
    },
    clearSelectedAntecedentGrossesse: (state) => {
      state.selectedAntecedentGrossesse = null
    }
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createAntecedentGrossesse.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createAntecedentGrossesse.fulfilled, (state, action) => {
        state.loading = false
        state.antecedentGrossesse.push(...action.payload)
      })
      .addCase(createAntecedentGrossesse.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Get All
      .addCase(getAllAntecedentGrossesse.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getAllAntecedentGrossesse.fulfilled, (state, action) => {
        state.loading = false
        state.antecedentGrossesse = action.payload
      })
      .addCase(getAllAntecedentGrossesse.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Get ById
      .addCase(getByIdAntecedentGrossesse.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getByIdAntecedentGrossesse.fulfilled, (state, action) => {
        state.loading = false
        state.selectedAntecedentGrossesse = action.payload
      })
      .addCase(getByIdAntecedentGrossesse.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Update
      .addCase(updateAntecedentGrossesse.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateAntecedentGrossesse.fulfilled, (state, action) => {
        state.loading = false
        const index = state.antecedentGrossesse.findIndex(ac => ac.id === action.payload.id)
        if (index !== -1) {
          state.antecedentGrossesse[index] = action.payload
        }
      })
      .addCase(updateAntecedentGrossesse.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
      // Delete
      .addCase(deleteAntecedentGrossesse.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteAntecedentGrossesse.fulfilled, (state, action) => {
        state.loading = false
        state.antecedentGrossesse = state.antecedentGrossesse.filter(ac => ac.id !== Number(action.payload))
      })
      .addCase(deleteAntecedentGrossesse.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  }
})

export const {
  setSelectedAntecedentGrossesse,
  setParite,
  setDate,
  setEstEnceinte,
  setNombreEnfants,
  setRemarks,
  resetAntecedentGrossesseState,
  clearSelectedAntecedentGrossesse
} = antecedentGrossesseSlice.actions
export default antecedentGrossesseSlice.reducer
