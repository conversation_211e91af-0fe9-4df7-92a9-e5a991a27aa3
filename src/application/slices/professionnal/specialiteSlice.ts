import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ListeSpecialites, SpecialiteProfessionnel } from "@/domain/models";
import { professionnels_categories_enum } from "@/domain/models/enums";
import GetSpecialitiesRepository from "@/infrastructure/repositories/professionalSpecialities/GetSpecialitiesRepository";
import GetSpecialiteByIdRepository from "@/infrastructure/repositories/professionalSpecialities/GetSpecialiteByIdRepository";
import GetSpecialitesByCategorieRepository from "@/infrastructure/repositories/professionalSpecialities/GetSpecialitesByCategorieRepository";
import CreateSpecialiteRepository from "@/infrastructure/repositories/professionalSpecialities/CreateSpecialiteRepository";
import UpdateSpecialiteRepository from "@/infrastructure/repositories/professionalSpecialities/UpdateSpecialiteRepository";
import DeleteSpecialiteRepository from "@/infrastructure/repositories/professionalSpecialities/DeleteSpecialiteRepository";
import GetSpecialitiesUsecase from "@/domain/usecases/professionalSpecialities/GetSpecialitiesUsecase";
import GetSpecialiteByIdUsecase from "@/domain/usecases/professionalSpecialities/GetSpecialiteByIdUsecase";
import GetSpecialitesByCategorieUsecase from "@/domain/usecases/professionalSpecialities/GetSpecialitesByCategorieUsecase";
import CreateSpecialiteUsecase from "@/domain/usecases/professionalSpecialities/CreateSpecialiteUsecase";
import UpdateSpecialiteUsecase from "@/domain/usecases/professionalSpecialities/UpdateSpecialiteUsecase";
import DeleteSpecialiteUsecase from "@/domain/usecases/professionalSpecialities/DeleteSpecialiteUsecase";

// Fonction utilitaire pour convertir ListeSpecialites en SpecialiteProfessionnel
const mapListeSpecialitesToSpecialiteProfessionnel = (
  specialites: ListeSpecialites[]
): SpecialiteProfessionnel[] => {
  return specialites.map((specialite) => ({
    id: specialite.id,
    nom_specialite: specialite.nom_specialite,
    id_professionnel: 0, // Valeur par défaut, sera remplacée lors de l'association à un professionnel
    type_etablissement:
      specialite.id_type_etablissement as unknown as professionnels_categories_enum,
  }));
};

interface SpecialiteState {
  specialites: SpecialiteProfessionnel[];
  loading: boolean;
  error: string | null;
  selectedSpecialite: SpecialiteProfessionnel | null;
}

const initialState: SpecialiteState = {
  specialites: [],
  loading: false,
  error: null,
  selectedSpecialite: null,
};

// Initialisation des repositories
const getSpecialitiesRepository = new GetSpecialitiesRepository();
const getSpecialiteByIdRepository = new GetSpecialiteByIdRepository();
const getSpecialitesByCategorieRepository =
  new GetSpecialitesByCategorieRepository();
const createSpecialiteRepository = new CreateSpecialiteRepository();
const updateSpecialiteRepository = new UpdateSpecialiteRepository();
const deleteSpecialiteRepository = new DeleteSpecialiteRepository();

// Initialisation des usecases
const getSpecialitiesUsecase = new GetSpecialitiesUsecase(
  getSpecialitiesRepository
);
const getSpecialiteByIdUsecase = new GetSpecialiteByIdUsecase(
  getSpecialiteByIdRepository
);
const getSpecialitesByCategorieUsecase = new GetSpecialitesByCategorieUsecase(
  getSpecialitesByCategorieRepository
);
const createSpecialiteUsecase = new CreateSpecialiteUsecase(
  createSpecialiteRepository
);
const updateSpecialiteUsecase = new UpdateSpecialiteUsecase(
  updateSpecialiteRepository
);
const deleteSpecialiteUsecase = new DeleteSpecialiteUsecase(
  deleteSpecialiteRepository
);

// Async thunks
export const fetchSpecialites = createAsyncThunk(
  "specialites/fetchAll",
  async (_, { rejectWithValue }) => {
    try {
      const data = await getSpecialitiesUsecase.execute();
      return mapListeSpecialitesToSpecialiteProfessionnel(data);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchSpecialitesByCategorie = createAsyncThunk(
  "specialites/fetchByCategorie",
  async (categorieId: number, { rejectWithValue }) => {
    try {
      const data = await getSpecialitesByCategorieUsecase.execute(categorieId);
      // Pas besoin de mapper car le type est déjà SpecialiteProfessionnel[]
      return data;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const createSpecialite = createAsyncThunk(
  "specialites/create",
  async (
    newSpecialite: Omit<SpecialiteProfessionnel, "id">,
    { rejectWithValue }
  ) => {
    try {
      const createdSpecialite =
        await createSpecialiteUsecase.execute(newSpecialite);
      return createdSpecialite;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const updateSpecialite = createAsyncThunk(
  "specialites/update",
  async (
    { id, updates }: { id: number; updates: Partial<SpecialiteProfessionnel> },
    { rejectWithValue }
  ) => {
    try {
      const updatedSpecialite = await updateSpecialiteUsecase.execute(
        id,
        updates
      );
      return updatedSpecialite;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const deleteSpecialite = createAsyncThunk(
  "specialites/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      await deleteSpecialiteUsecase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const specialiteSlice = createSlice({
  name: "specialites",
  initialState,
  reducers: {
    setSelectedSpecialite(
      state,
      action: PayloadAction<SpecialiteProfessionnel>
    ) {
      state.selectedSpecialite = action.payload;
    },
    clearSelectedSpecialite(state) {
      state.selectedSpecialite = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch all specialites
      .addCase(fetchSpecialites.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSpecialites.fulfilled, (state, action) => {
        state.loading = false;
        state.specialites = action.payload;
      })
      .addCase(fetchSpecialites.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Fetch by categorie
      .addCase(fetchSpecialitesByCategorie.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSpecialitesByCategorie.fulfilled, (state, action) => {
        state.loading = false;
        state.specialites = action.payload;
      })
      .addCase(fetchSpecialitesByCategorie.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Create specialite
      .addCase(createSpecialite.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSpecialite.fulfilled, (state, action) => {
        state.loading = false;
        state.specialites.push(action.payload);
      })
      .addCase(createSpecialite.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update specialite
      .addCase(updateSpecialite.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSpecialite.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.specialites.findIndex(
          (s) => s.id === action.payload.id
        );
        if (index !== -1) {
          state.specialites[index] = action.payload;
        }
      })
      .addCase(updateSpecialite.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete specialite
      .addCase(deleteSpecialite.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSpecialite.fulfilled, (state, action) => {
        state.loading = false;
        state.specialites = state.specialites.filter(
          (s) => s.id !== action.payload
        );
      })
      .addCase(deleteSpecialite.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSelectedSpecialite, clearSelectedSpecialite } =
  specialiteSlice.actions;
export default specialiteSlice.reducer;
