import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Commune, District, Province, Region } from "@/domain/models";

// Repositories
import GetProvincesRepository from "@/infrastructure/repositories/province/GetProvincesRepository";
import {
  GetDistrictsInRegionRepository,
  GetDistrictsRepository,
} from "@/infrastructure/repositories/district";
import { GetCommunesInDistrictRepository } from "@/infrastructure/repositories/commune";
import GetRegionsInProvinceRepository from "@/infrastructure/repositories/Region/GetRegionsInProvinceRepository";

// Usecases
import GetProvincesUsecase from "@/domain/usecases/province/GetProvincesUsecase";
import {
  GetDistrictsInRegionUsecase,
  GetDistrictsUsecase,
} from "@/domain/usecases/district";
import { GetCommunesInDistrictUsecase } from "@/domain/usecases/commune";
import GetRegionsInProvinceUsecase from "@/domain/usecases/Region/GetRegionsInProvinceUsecase";
import GetRegionsRepository from "@/infrastructure/repositories/Region/GetRegionsRepository";
import GetRegionsUsecase from "@/domain/usecases/Region/GetRegionsUsecase";

// Structure de l'état pour la couverture (landing page)
export interface LocationState {
  provinces: Province[];
  regions: Region[];
  districts: District[];
  communes: Commune[];
  selectedProvince: Province | null;
  selectedRegion: Region | null;
  selectedDistrict: District | null;
  selectedCommune: Commune | null;
  isLoading: boolean;
  error: string | null;
}

// Etat initial
const initialState: LocationState = {
  provinces: [],
  regions: [],
  districts: [],
  communes: [],
  selectedProvince: null,
  selectedRegion: null,
  selectedDistrict: null,
  selectedCommune: null,
  isLoading: false,
  error: null,
};

export const getProvinces = createAsyncThunk(
  "location/getProvinces",
  async (_, { rejectWithValue }) => {
    try {
      const getProvincesRepository = new GetProvincesRepository();
      const getProvincesUsecase = new GetProvincesUsecase(
        getProvincesRepository,
      );

      const provinces = await getProvincesUsecase.execute();

      return provinces;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const getRegionsByProvince = createAsyncThunk(
  "location/getRegionsByProvince",
  async (province_id: number, { rejectWithValue }) => {
    try {
      const getRegionsInProvinceRepository =
        new GetRegionsInProvinceRepository();
      const getRegionsInProvinceUsecase = new GetRegionsInProvinceUsecase(
        getRegionsInProvinceRepository,
      );

      const regions = await getRegionsInProvinceUsecase.execute(province_id);

      return regions;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const getDistrictsByRegion = createAsyncThunk(
  "location/getDistrictsByRegion",
  async (region_id: number, { rejectWithValue }) => {
    try {
      const getDistrictsInRegionRepository =
        new GetDistrictsInRegionRepository();
      const getDistrictsInRegionUsecase = new GetDistrictsInRegionUsecase(
        getDistrictsInRegionRepository,
      );

      const districts = await getDistrictsInRegionUsecase.execute(region_id);

      return districts;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const getCommunesByDistrict = createAsyncThunk(
  "location/getCommunesByDistrict",
  async (district_id: number, { rejectWithValue }) => {
    try {
      const getCommunesInDistrictRepository =
        new GetCommunesInDistrictRepository();
      const getCommunesInDistrictUsecase = new GetCommunesInDistrictUsecase(
        getCommunesInDistrictRepository,
      );

      const communes = await getCommunesInDistrictUsecase.execute(district_id);

      return communes;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const filterRegionByProvince = createAsyncThunk(
  "location/filterRegionByProvince",
  async (province_id: number, { rejectWithValue }) => {
    try {
      const getRegionsInProvinceRepository =
        new GetRegionsInProvinceRepository();
      const getRegionsInProvinceUsecase = new GetRegionsInProvinceUsecase(
        getRegionsInProvinceRepository,
      );

      const regions = await getRegionsInProvinceUsecase.execute(province_id);

      return regions;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const filterDistrictsByRegion = createAsyncThunk(
  "location/filterDistrictsByRegion",
  async (region_id: number, { rejectWithValue }) => {
    try {
      const getDistrictsInRegionRepository =
        new GetDistrictsInRegionRepository();
      const getDistrictsInRegionUsecase = new GetDistrictsInRegionUsecase(
        getDistrictsInRegionRepository,
      );

      const districts = await getDistrictsInRegionUsecase.execute(region_id);

      return districts;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const filterCommunesByDistrict = createAsyncThunk(
  "location/filterCommunesByDistrict",
  async (district_id: number, { rejectWithValue }) => {
    try {
      const getCommunesInDistrictRepository =
        new GetCommunesInDistrictRepository();
      const getCommunesInDistrictUsecase = new GetCommunesInDistrictUsecase(
        getCommunesInDistrictRepository,
      );

      const communes = await getCommunesInDistrictUsecase.execute(district_id);

      return communes;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const getDistricts = createAsyncThunk(
  "location/getDistricts",
  async (_, { rejectWithValue }) => {
    try {
      const getDistrictsRepository = new GetDistrictsRepository();
      const getDistrictsUsecase = new GetDistrictsUsecase(
        getDistrictsRepository,
      );

      const districts = await getDistrictsUsecase.execute();

      return districts;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const getRegions = createAsyncThunk(
  "location/getRegions",
  async (_, { rejectWithValue }) => {
    try {
      const getRegionsRepository = new GetRegionsRepository();
      const getRegionsUsecase = new GetRegionsUsecase(
        getRegionsRepository,
      );

      const regions = await getRegionsUsecase.execute();

      return regions;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

/**
 * Gestion des actions de modification de localisation
 */
const locationSlice = createSlice({
  name: "location",
  initialState,
  reducers: {
    setState: (state, action: PayloadAction<Partial<LocationState>>) => {
      state.selectedProvince = action.payload.selectedProvince;
      state.selectedRegion = action.payload.selectedRegion;
      state.selectedDistrict = action.payload.selectedDistrict;
      state.selectedCommune = action.payload.selectedCommune;
    },

    setSelectedProvince: (state, action: PayloadAction<Province>) => {
      state.selectedProvince = action.payload;
      state.selectedRegion = null;
      state.selectedDistrict = null;
      state.selectedCommune = null;
    },
    setSelectedRegion: (state, action: PayloadAction<Region>) => {
      state.selectedRegion = action.payload;
      state.selectedDistrict = null;
      state.selectedCommune = null;
    },
    setSelectedDistrict: (state, action: PayloadAction<District>) => {
      state.selectedDistrict = action.payload;
      state.selectedCommune = null;
    },
    setSelectedCommune: (state, action: PayloadAction<Commune>) => {
      state.selectedCommune = action.payload;
    },
    resetLocations: (state) => {
      Object.assign(state, initialState);
    },
  },
  extraReducers: (builder) => {
    builder
      // Gestion des actions asynchrones
      .addCase(getProvinces.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProvinces.fulfilled, (state, action) => {
        state.isLoading = false;
        state.provinces = action.payload;
      })
      .addCase(getProvinces.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      .addCase(getRegionsByProvince.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getRegionsByProvince.fulfilled, (state, action) => {
        state.isLoading = false;
        state.regions = action.payload;
      })
      .addCase(getRegionsByProvince.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      .addCase(getRegions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getRegions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.regions = action.payload;
      })
      .addCase(getRegions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      .addCase(getDistrictsByRegion.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getDistrictsByRegion.fulfilled, (state, action) => {
        state.isLoading = false;
        state.districts = action.payload;
      })
      .addCase(getDistrictsByRegion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      .addCase(getCommunesByDistrict.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCommunesByDistrict.fulfilled, (state, action) => {
        state.isLoading = false;
        state.communes = action.payload;
      })
      .addCase(getCommunesByDistrict.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      .addCase(filterRegionByProvince.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(filterRegionByProvince.fulfilled, (state, action) => {
        state.isLoading = false;
        state.regions = action.payload;
      })
      .addCase(filterRegionByProvince.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      .addCase(filterDistrictsByRegion.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(filterDistrictsByRegion.fulfilled, (state, action) => {
        state.isLoading = false;
        state.districts = action.payload;
      })
      .addCase(filterDistrictsByRegion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      .addCase(filterCommunesByDistrict.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(filterCommunesByDistrict.fulfilled, (state, action) => {
        state.isLoading = false;
        state.communes = action.payload;
      })
      .addCase(filterCommunesByDistrict.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      .addCase(getDistricts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getDistricts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.districts = action.payload;
      })
      .addCase(getDistricts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setState,
  setSelectedProvince,
  setSelectedRegion,
  setSelectedDistrict,
  setSelectedCommune,
  resetLocations,
} = locationSlice.actions;

export default locationSlice.reducer;
