import { AvailabilitySettingsDTO } from '@/domain/DTOS';
import { controle_parametre, horaire_date_specifique, horaire_hebdomadaire } from '@/domain/models';
import { DEFAULT_SETTINGS } from '@/shared/constants/DefaultSettings';
import { DEFAULT_SETTINGS_LOCAL } from '@/shared/constants/DefaultSettingsLocal';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface availabilitySettignsStateSlice {
  settings: AvailabilitySettingsDTO;
  settingsLocal: controle_parametre;
  errors: { [key: string]: string };
}

const initialState: availabilitySettignsStateSlice = {
  settings: DEFAULT_SETTINGS,
  settingsLocal: DEFAULT_SETTINGS_LOCAL,
  errors: {}
};

const availabilitySettignsStateSlice = createSlice({
  name: 'availability',
  initialState,
  reducers: {
    setSchedule: (state, action: PayloadAction<horaire_hebdomadaire[]>) => {
      state.settings.horaire_hebdomadaire = action.payload;
    },
    setAvailabilityType: (state, action: PayloadAction<string>) => {
      state.settings.type = action.payload as "hebdomadaire" | "specifique";
    },
    setDateSchedule: (state, action: PayloadAction<horaire_date_specifique[]>) => {
      state.settings.horaire_date_specifique = action.payload;
    },
    setDateException: (state, action: PayloadAction<horaire_date_specifique[]>) => {
      state.settings.horaire_date_specifique = action.payload;
    },
    setDureePause: (state, action: PayloadAction<number>) => {
      state.settings.duree_pause = action.payload;
    },
    setMaxReservationsPerDay: (state, action: PayloadAction<number>) => {
      state.settings.max_rdv_par_jours = action.payload;
    },
    setCanInviteOthers: (state, action: PayloadAction<boolean>) => {
      state.settings.peut_inviter_autre = action.payload;
    },
    setStartDate: (state, action: PayloadAction<string | null>) => {
      state.settings.date_debut = action.payload;
    },
    setEndDate: (state, action: PayloadAction<string | null>) => {
      state.settings.date_fin = action.payload;
    },

    setPlanningType: (state, action: PayloadAction<string>) => {
      state.settingsLocal.planningType = action.payload as "now" | "date";
    },
    setDuration: (state, action: PayloadAction<number>) => {
      state.settings.temps_moyen_consulation = action.payload;
    },
    setIsMaxDays: (state, action: PayloadAction<boolean>) => {
      state.settingsLocal.isMaxDays = action.payload;
    },
    setIsMinHours: (state, action: PayloadAction<boolean>) => {
      state.settingsLocal.isMinHours = action.payload;
    },
    setIsBreak: (state, action: PayloadAction<boolean>) => {
      state.settingsLocal.isBreak = action.payload;
    },
    setIsMaxReservationsPerDay: (state, action: PayloadAction<boolean>) => {
      state.settingsLocal.isMaxReservationsPerDay = action.payload;
    },
    setMaxDays: (state, action: PayloadAction<number>) => {
      state.settingsLocal.maxDays = action.payload;
    },
    setMinHours: (state, action: PayloadAction<number>) => {
      state.settingsLocal.minHours = action.payload;
    },
    setBreakDuration: (state, action: PayloadAction<string>) => {
      state.settingsLocal.tempPause = action.payload;
    },

    setSettingsLocal: (state, action: PayloadAction<controle_parametre>) => {
      state.settingsLocal = action.payload;
    },
    initializeSettings: (state, action: PayloadAction<{
      initialSettings: AvailabilitySettingsDTO,
      initialSettingsLocal: controle_parametre
    }>) => {
      const { initialSettings, initialSettingsLocal } = action.payload;
      state.settings = initialSettings;
      state.settingsLocal = initialSettingsLocal;
    },

    setErrors: (state, action: PayloadAction<{ [key: string]: string }>) => {
      state.errors = action.payload;
    },
    updateSettings: (state, action: PayloadAction<AvailabilitySettingsDTO>) => {
      state.settings = action.payload;
    },
  }
});

export const {
  setSchedule,
  setAvailabilityType,
  setDateSchedule,
  setDateException,
  setDureePause,
  setMaxReservationsPerDay,
  setCanInviteOthers,
  setStartDate,
  setEndDate,
  setPlanningType,
  setDuration,
  setIsMaxDays,
  setIsMinHours,
  setIsBreak,
  setIsMaxReservationsPerDay,
  setMaxDays,
  setMinHours,
  setBreakDuration,
  setSettingsLocal,
  initializeSettings,
  setErrors,
  updateSettings
} = availabilitySettignsStateSlice.actions;

export default availabilitySettignsStateSlice.reducer;
