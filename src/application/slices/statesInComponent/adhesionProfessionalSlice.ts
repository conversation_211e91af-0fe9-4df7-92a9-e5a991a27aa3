import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface AdhesionProfessionalState {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    location: {
        province: string;
        region: string;
        district: string;
        commune: string;
    };
    typeEtablissement: string;
    villeCabinet: string;
    errors: { [key: string]: string };
}

const initialState: AdhesionProfessionalState = {
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    location: {
        province: '',
        region: '',
        district: '',
        commune: ''
    },
    typeEtablissement: '',
    villeCabinet: '',
    errors: {}
};

const adhesionProfessionalSlice = createSlice({
    name: 'adhesionProfessional',
    initialState,
    reducers: {
        setField: (state, action: PayloadAction<{ field: string; value: string }>) => {
            const { field, value } = action.payload;
            state[field as keyof Omit<AdhesionProfessionalState, 'location' | 'errors'>] = value;
            // Clear error when field is updated
            if (state.errors[field]) {
                delete state.errors[field];
            }
        },
        setLocation: (state, action: PayloadAction<{ field: string; value: string }>) => {
            const { field, value } = action.payload;
            state.location[field as keyof typeof state.location] = value;
            if (state.errors[`location.${field}`]) {
                delete state.errors[`location.${field}`];
            }
        },
        setErrors: (state, action: PayloadAction<{ [key: string]: string }>) => {
            state.errors = action.payload;
        },
        clearErrors: (state) => {
            state.errors = {};
        },
        resetForm: () => initialState
    }
});

export const {
    setField,
    setLocation,
    setErrors,
    clearErrors,
    resetForm
} = adhesionProfessionalSlice.actions;

export default adhesionProfessionalSlice.reducer;
