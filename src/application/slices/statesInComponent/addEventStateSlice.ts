import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface addEventStateSlice {
    title: string;
    est_toute_la_journee: boolean;
    description: string;
    est_reporte: boolean;
    date_debut: string | null;
    date_fin: string | null;
    heure_debut: string;
    heure_fin: string;
    repetition: "once" | "allDays" | "allWeeks" | "weekly" | "allMonths" | "allYears";
}

// Fonction utilitaire pour convertir une date locale en ISO string avec le bon fuseau horaire
const getLocalISOString = (date: Date) => {
    const offset = date.getTimezoneOffset();
    const localDate = new Date(date.getTime() - (offset * 60 * 1000));
    return localDate.toISOString();
};

const date_debut = new Date();
const date_fin = new Date();

// Ajouter les heures en respectant le fuseau horaire local
date_debut.setHours(date_debut.getHours() + 2, 0, 0, 0);
date_fin.setHours(date_fin.getHours() + 3, 0, 0, 0);

const initialState: addEventStateSlice = {
    title: '',
    est_toute_la_journee: false,
    description: '',
    est_reporte: false,
    date_debut: getLocalISOString(date_debut),
    date_fin: getLocalISOString(date_fin),
    heure_debut: `${date_debut.getHours().toString().padStart(2, '0')}:00`,
    heure_fin: `${date_fin.getHours().toString().padStart(2, '0')}:00`,
    repetition: 'once',
};

const addEventStateSlice = createSlice({
    name: 'addEventStateSlice',
    initialState,
    reducers: {
        setTitle: (state, action: PayloadAction<string>) => {
            state.title = action.payload;
        },
        setIsAllDay: (state, action: PayloadAction<boolean>) => {
            state.est_toute_la_journee = action.payload;
        },
        setDescription: (state, action: PayloadAction<string>) => {
            state.description = action.payload;
        },
        setIsReported: (state, action: PayloadAction<boolean>) => {
            state.est_reporte = action.payload;
        },
        setSelectedDateDebut: (state, action: PayloadAction<string | null>) => {
            state.date_debut = action.payload;
        },
        setSelectedDateFin: (state, action: PayloadAction<string | null>) => {
            state.date_fin = action.payload;
        },
        setStartTime: (state, action: PayloadAction<string>) => {
            state.heure_debut = action.payload;
        },
        setEndTime: (state, action: PayloadAction<string>) => {
            state.heure_fin = action.payload;
        },
        setRepetition: (state, action: PayloadAction<string>) => {
            state.repetition = action.payload as "once" | "allDays" | "allWeeks" | "weekly" | "allMonths" | "allYears";
        },
        resetState: () => initialState,
    }
});

export const {
    setTitle,
    setIsAllDay,
    setDescription,
    setIsReported,
    setSelectedDateDebut,
    setSelectedDateFin,
    setStartTime,
    setEndTime,
    resetState,
    setRepetition,
} = addEventStateSlice.actions;

export default addEventStateSlice.reducer;
