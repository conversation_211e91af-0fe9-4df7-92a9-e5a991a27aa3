import { Utilisateur } from "@/domain/models";
import { userRepository } from "@/infrastructure/repositories";
import { SupabaseError } from "@/infrastructure/supabase/supabaseError";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
interface UserState {
  patients: Utilisateur[] | null;
  loading: boolean;
  error: SupabaseError | null;
  selectedPatient: Utilisateur | null;
}

const initialState: UserState = {
  patients: [],
  loading: false,
  error: null,
  selectedPatient: null,
};

export const getUsers = createAsyncThunk(
  "user/getUsers",
  async (_, { rejectWithValue }) => {
    try {
      const users = await userRepository.getUsers();
      return users;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

/**
 * @returns L'utilisateur ajouté
 */
export const addUser = createAsyncThunk(
  "user/addUser",
  async (userInfo: Omit<Utilisateur, "id" | "cree_a" | "mis_a_jour_a">) => {
    const response = await userRepository.createUser(userInfo);
    return response;
  },
);

/**
 * @param id - L'ID de l'utilisateur à mettre à jour
 * @param data - Les données à mettre à jour avec les nouvelles valeurs
 * @returns L'utilisateur mis à jour
 */
export const updateUser = createAsyncThunk(
  "user/updateUser",
  async ({ id, data }: { id: number; data: Utilisateur }) => {
    const response = await userRepository.editUser(id, data);
    return response;
  },
);

/*
 * @param id - L'ID de l'utilisateur à supprimer
 * @returns L'ID de l'utilisateur supprimé
 */
export const deleteUser = createAsyncThunk(
  "user/deleteUser",
  async ({ id }: { id: number }) => {
    if (!id) {
      throw new Error("aucun utilisateur sélectionné pour suppression.");
    }
    await userRepository.deleteUser(id);

    return id; // Retourne l'ID supprimé
  },
);

const userSlice = createSlice({
  name: "patient",
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<SupabaseError>) => {
      state.error = action.payload;
    },
    setSelectedUser: (state, action: PayloadAction<Utilisateur>) => {
      state.selectedPatient = action.payload;
    },
    getUserById: (state, action: PayloadAction<number>) => {
      const matchingUser = state.patients.find(
        (user) => user.id === action.payload,
      )[0];
      return matchingUser;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getUsers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.patients = action.payload;
      })
      .addCase(getUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as SupabaseError;
      })
      // Ajout d'utilisateur
      .addCase(addUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addUser.fulfilled, (state, action) => {
        state.patients.push(action.payload);
        state.loading = false;
      })
      .addCase(addUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as SupabaseError;
      })
      // Mise à jour de l'utilisateur
      .addCase(updateUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        const index = state.patients.findIndex(
          (user) => user.id === action.payload.id,
        );
        if (index !== -1) state.patients[index] = action.payload;
        state.loading = false;
        state.selectedPatient = null;
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as SupabaseError;
      })
      // Suppression de l'utilisateur
      .addCase(deleteUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.patients = state.patients.filter(
          (user) => user.id !== action.payload,
        );
        state.selectedPatient = null;
        state.loading = false;
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as SupabaseError;
      });
  },
});

export const { setError, setLoading, setSelectedUser, getUserById } =
  userSlice.actions;
export default userSlice.reducer;
