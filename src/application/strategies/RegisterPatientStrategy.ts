import { IRegisterStrategy } from "@/domain/interfaces/strategies/IRegisterStrategy";
import { Patient } from "@/domain/models";
import { registerProps } from "@/domain/interfaces/usecases/Register/IRegisterUserUsecase";
import { RegisterUserDTO } from "@/domain/DTOS/RegisterUserDTO";
import { IMatriculeGenerator } from "@/domain/interfaces/services/IMatriculeGenerator";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { ICreatePatientRepository } from "@/domain/interfaces/repositories/patients";

export class RegisterPatientStrategy implements IRegisterStrategy {
  constructor(
    private readonly createPatientRepository: ICreatePatientRepository,
    private readonly matriculeGenerator: IMatriculeGenerator,
  ) {}

  async register(
    data: registerProps,
    userId: number,
  ): Promise<RegisterUserDTO> {
    if (data.role !== utilisateurs_role_enum.PATIENT) {
      throw new Error("Invalid role for RegisterPatientStrategy");
    }

    const unique_id = this.matriculeGenerator.generateMatricule(
      userId,
      (data.additionnalInfo as Omit<Patient, "id" | "utilisateur_id">).sexe,
    );

    const patientData = await this.createPatientRepository.execute({
      utilisateur_id: userId,
      unique_id: unique_id,
      ...(data.additionnalInfo as Omit<Patient, "id" | "utilisateur_id">),
    });

    return {
      user: null,
      userData: patientData,
      success: true,
    };
  }
}
