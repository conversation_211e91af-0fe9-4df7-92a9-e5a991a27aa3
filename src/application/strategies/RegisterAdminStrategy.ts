import { IRegisterStrategy } from "@/domain/interfaces/strategies/IRegisterStrategy";
import { ICreateAdminRepository } from "@/domain/interfaces/repositories/admins";
import { RegisterUserDTO } from "@/domain/DTOS";
import { registerProps } from "@/domain/interfaces/usecases/Register/IRegisterUserUsecase";

export class RegisterAdminStrategy implements IRegisterStrategy {
  constructor(private readonly adminRepository: ICreateAdminRepository) {}

  async register(
    data: registerProps,
    userId: number
  ): Promise<RegisterUserDTO> {
    const adminData = await this.adminRepository.execute({
      utilisateur_id: userId,
      nom: data.additionnalInfo.nom,
    });

    return {
      user: null,
      userData: adminData,
      success: true,
    };
  }
}
