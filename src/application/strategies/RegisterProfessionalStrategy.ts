import { IRegisterStrategy } from "@/domain/interfaces/strategies/IRegisterStrategy";
import { IProfessionalRepository } from "@/domain/interfaces/repositories";
import { RegisterUserDTO } from "@/domain/DTOS";
import { Professionnel } from "@/domain/models";
import { registerProps } from "@/domain/interfaces/usecases/Register/IRegisterUserUsecase";
import { IMatriculeGenerator } from "@/domain/interfaces/services/IMatriculeGenerator";
import { sexe_enum } from "@/domain/models/enums";

export class RegisterProfessionalStrategy implements IRegisterStrategy {
  constructor(
    private readonly professionalRepository: IProfessionalRepository,
  ) { }

  async register(
    data: registerProps,
    userId: number
  ): Promise<RegisterUserDTO> {
    const professionalData =
      await this.professionalRepository.createProfessional({
        utilisateur_id: userId,
        ...(data.additionnalInfo as Professionnel),
      });

    return {
      user: null,
      userData: professionalData,
      success: true,
    };
  }
}
