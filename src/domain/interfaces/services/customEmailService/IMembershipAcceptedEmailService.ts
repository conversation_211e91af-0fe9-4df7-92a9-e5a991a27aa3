export interface MembershipAcceptedRequest {
  to: string;
  FirstName: string;
  token: string;
}

export interface MembershipAcceptedSuccess {
  message: string;
}

export interface MembershipAcceptedError {
  error: string;
  details?: string;
}

export interface IMembershipAcceptedEmailService {
  execute(
    params: MembershipAcceptedRequest
  ): Promise<MembershipAcceptedSuccess | MembershipAcceptedError>;
}
