import { RegisterUserDTO } from "@/domain/DTOS/RegisterUserDTO";
import { administrateurs, Contact, Patient, Professionnel } from "@/domain/models";
import { utilisateurs_role_enum } from "@/domain/models/enums";

export type registerProps =
  | {
    role: utilisateurs_role_enum.PATIENT;
    email: string;
    password: string;
    additionnalInfo: Omit<Patient, "id" | "utilisateur_id" | "unique_id">;
    contact?: Omit<Contact, "id">[];
  }
  | {
    role: utilisateurs_role_enum.PROFESSIONNEL;
    email: string;
    password: string;
    additionnalInfo: Omit<Professionnel, "id" | "utilisateur_id">;
    contact?: Omit<Contact, "id">[];
  }
  | {
    role: utilisateurs_role_enum.ADMIN;
    email: string;
    password: string;
    additionnalInfo: Omit<administrateurs, "id" | "utilisateur_id">;
    contact?: Omit<Contact, "id">[];
  };

export interface IRegisterUserUsecase {
  execute(data: registerProps): Promise<RegisterUserDTO>;
}
