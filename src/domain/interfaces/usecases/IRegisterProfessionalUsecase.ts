import { RegisterProfessionalDTO } from "@/domain/DTOS";
import { professionnels_categories_enum } from "@/domain/models/enums";
import { RegisterProfessionalResponse } from "@/domain/usecases/user/Register/RegisterProfessionalUseCase";

export interface IRegisterProfessionalUsecase {
  execute(data: RegisterProfessionalDTO, professionalCategory: professionnels_categories_enum): Promise<RegisterProfessionalResponse>;
}
