import { Professionnel, Utilisateur } from "@/domain/models";
import { User } from "@supabase/supabase-js";
import { CabinetMedicalFormDTO } from "@/domain/DTOS/CabinetMedicalFormDTO.ts";

export interface IRegisterCabinetMedical {
  execute(
    formData: CabinetMedicalFormDTO,
  ): Promise<{
    authData: User;
    user: Utilisateur;
    userData: Professionnel;
    success: boolean;
    message?: string
  }>;
}
