import { historiques_medicaux } from '@/domain/models'

/**
 * Interface for managing medical histories
 * Following Interface Segregation Principle by grouping related medical history methods
 */
export interface IMedicalHistoryRepository {
  /**
   * Retrieves all medical histories
   */
  getAllMedicalHistories: () => Promise<historiques_medicaux[]>

  /**
   * Retrieves all medical histories for a professional
   * @param professionalId - The professional's ID
   */
  getMedicalHistoriesByProfessionalId: (professionalId: number) => Promise<historiques_medicaux[]>

  /**
   * Retrieves medical history for a patient
   * @param patientId - The patient's ID
   */
  getMedicalHistoryByPatientId: (patientId: number) => Promise<historiques_medicaux>

  /**
   * Retrieves medical history by action ID
   * @param actionId - The action's ID
   */
  getMedicalHistoryByActionId: (actionId: number) => Promise<historiques_medicaux>

  /**
   * Retrieves medical history by consultation ID
   * @param consultationId - The consultation's ID
   */
  getMedicalHistoryByConsultationId: (consultationId: number) => Promise<historiques_medicaux>

  /**
   * Retrieves a specific medical history by ID
   * @param id - The medical history ID
   */
  getMedicalHistoryById: (id: number) => Promise<historiques_medicaux>

  /**
   * Creates a new medical history
   * @param history - The medical history data without ID
   */
  createMedicalHistory: (history: Omit<historiques_medicaux, 'id'>) => Promise<historiques_medicaux>

  /**
   * Updates an existing medical history
   * @param id - The medical history ID
   * @param data - The partial medical history data to update
   */
  updateMedicalHistory: (
    id: number,
    data: Partial<historiques_medicaux>
  ) => Promise<historiques_medicaux>

  /**
   * Deletes a medical history
   * @param id - The medical history ID
   */
  deleteMedicalHistory: (id: number) => Promise<void>
}
