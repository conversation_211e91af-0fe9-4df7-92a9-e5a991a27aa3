import { ListeMotCle, MotClesProfessionnel } from '../entities'

export interface IMotCleRepository {
  getAllMotCles: () => Promise<ListeMotCle[]>
  getAllMotClesById: (id: number) => Promise<ListeMotCle>
  getMotCleById: (id: number) => Promise<MotClesProfessionnel>
  getAllProfessionnalMotCles: (professionnal_id: number) => Promise<MotClesProfessionnel[]>
  addProfessionnalMotClee: (
    newMotClee: Omit<MotClesProfessionnel, 'id'>
  ) => Promise<MotClesProfessionnel>
  deleteProfessionnalMotClee: (id: number) => Promise<MotClesProfessionnel>
}
