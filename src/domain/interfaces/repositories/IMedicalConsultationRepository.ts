import { consultation_medical } from '@/domain/models'

/**
 * Interface for managing medical consultations
 * Following Interface Segregation Principle by grouping related consultation methods
 */
export interface IMedicalConsultationRepository {
  /**
   * Retrieves a specific consultation by ID
   * @param id - The consultation ID
   */
  getConsultation: (id: number) => Promise<consultation_medical>

  /**
   * Retrieves all consultations for a professional
   * @param professionalId - The professional's ID
   */
  getConsultationsByProfessionalId: (professionalId: number) => Promise<consultation_medical[]>

  /**
   * Retrieves all consultations for a patient
   * @param patientId - The patient's ID
   */
  getConsultationsByPatientId: (patientId: number) => Promise<consultation_medical[]>

  /**
   * Creates a new consultation
   * @param consultationData - The consultation data without ID
   */
  createConsultation: (
    consultationData: Omit<consultation_medical, 'id'>
  ) => Promise<consultation_medical>

  /**
   * Updates an existing consultation
   * @param id - The consultation ID
   * @param updateData - The partial consultation data to update
   */
  updateConsultation: (
    id: number,
    updateData: Partial<consultation_medical>
  ) => Promise<consultation_medical>

  /**
   * Deletes a consultation
   * @param id - The consultation ID
   */
  deleteConsultation: (id: number) => Promise<consultation_medical>
}
