import {
  Professional_data,
  ProfessionalSearchParams,
  Professionnel,
} from "@/domain/models";

export interface IProfessionalRepository {
  getProfessionals: () => Promise<Professionnel[]>;
  getProfessionalById: (id: number) => Promise<Professionnel>;
  createProfessional: (
    professionalData: Omit<Professionnel, "id">
  ) => Promise<Professionnel>;
  updateProfessional: (
    id: number,
    professionalData: Partial<Professionnel>
  ) => Promise<Professionnel>;
  deleteProfessional: (id: number) => Promise<Professionnel>;
  getProfessionalByUserId: (id: number) => Promise<Professionnel>;
  getFilteredProfessionalsWithAllRelations: (
    params?: ProfessionalSearchParams
  ) => Promise<Professional_data[]>;
  getProfessionalWithAllRelationsById: (
    id: number
  ) => Promise<Professional_data>;
  filterBySpeciality: (specialityId: number) => Promise<Professionnel[]>;
  filterByLocation: (region: string) => Promise<Professionnel[]>;
}
