import { parametre_disponibilite } from '../entities'

export interface IScheduleRepository {
  getAllSchedules: () => Promise<parametre_disponibilite[]>
  getSchedulesByProffessionalId: (id: number) => Promise<parametre_disponibilite[]>
  getSchedulesById: (id: number) => Promise<parametre_disponibilite>
  createSchedule: (
    newSchedule: Omit<parametre_disponibilite, 'id'>
  ) => Promise<parametre_disponibilite>
  updateSchedule: (
    id: number,
    newData: Partial<parametre_disponibilite>
  ) => Promise<parametre_disponibilite>
  deleteSchedule: (id: number) => Promise<parametre_disponibilite>
}
