import { SpecialiteProfessionnel } from '../entities'

export interface ISpecialiteRepository {
  getAllSpecialites: () => Promise<SpecialiteProfessionnel[]>
  getSpecialiteById: (id: number) => Promise<SpecialiteProfessionnel>
  getSpecialiteByProfessionnalId: (id: number) => Promise<SpecialiteProfessionnel[]>
  getSpecialitesByCategorie: (categorieId: number) => Promise<SpecialiteProfessionnel[]>
  deleteSpecialite: (id: number) => Promise<SpecialiteProfessionnel>
  createSpecialite: (
    specialiteInformations: Omit<SpecialiteProfessionnel, 'id'>
  ) => Promise<SpecialiteProfessionnel>
  editSpecialite: (
    id: number,
    specialiteData: Partial<SpecialiteProfessionnel>
  ) => Promise<SpecialiteProfessionnel>
}
