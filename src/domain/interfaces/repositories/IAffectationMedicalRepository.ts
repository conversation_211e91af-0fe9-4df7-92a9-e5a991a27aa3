import { AffectationMedical } from '@/domain/models'

export interface IAffectationMedicalRepository {
  getAffectation: (id: number) => Promise<AffectationMedical>
  getAffectationsByPatient: (id_patient: number) => Promise<AffectationMedical[]>
  createAffectation: (
    affectationData: Omit<AffectationMedical, 'id'>
  ) => Promise<AffectationMedical>
  updateAffectation: (
    id: number,
    updatedData: Partial<Omit<AffectationMedical, 'id'>>
  ) => Promise<AffectationMedical>
  deleteAffectation: (id: number) => Promise<AffectationMedical>
}
