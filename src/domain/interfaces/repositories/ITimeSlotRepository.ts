import { creneau_horaire } from '@/domain/models'

export interface ITimeSlotRepository {
  getAvailableTimeSlots: (professionalId: number) => Promise<creneau_horaire[]>
  getAvailableTimeSlotsByProfessionalId: (professionalId: number) => Promise<creneau_horaire[]>
  getTimeslotById: (id: number) => Promise<creneau_horaire>
  createTimeSlot: (data: Omit<creneau_horaire, 'id'>[]) => Promise<creneau_horaire[]>
  updateTimeSlot: (data: Partial<creneau_horaire>) => Promise<creneau_horaire>
  deleteTimeSlot: (id: number) => Promise<creneau_horaire>
  cancelBooking: (timeSlotId: number) => Promise<void>
  bookTimeSlot: (timeSlotId: number, clientId: number) => Promise<void>
}
