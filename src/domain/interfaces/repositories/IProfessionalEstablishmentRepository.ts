import { EtablissementProfessionnel } from '@/domain/models'

export interface IProfessionalEstablishmentRepository {
  getAllEstablishments: () => Promise<EtablissementProfessionnel[]>

  getEstablishmentById: (id: number) => Promise<EtablissementProfessionnel>

  getEstablishmentByProfessionalId: (professionalId: number) => Promise<EtablissementProfessionnel>

  createEstablishment: (
    establishment: Omit<EtablissementProfessionnel, 'id'>
  ) => Promise<EtablissementProfessionnel>

  updateEstablishment: (
    id: number,
    data: Partial<EtablissementProfessionnel>
  ) => Promise<EtablissementProfessionnel>

  deleteEstablishment: (id: number) => Promise<EtablissementProfessionnel>
}
