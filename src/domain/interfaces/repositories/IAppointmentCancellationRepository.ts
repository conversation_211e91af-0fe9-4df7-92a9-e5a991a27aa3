import { AnnulerRendezVous } from '@/domain/models/AnnulerRendezVous'

/**
 * Interface for managing appointment cancellations
 * Following Interface Segregation Principle (ISP) by including only related methods
 */
export interface IAppointmentCancellationRepository {
  /**
   * Retrieves a specific cancellation by its ID
   * @param id - The unique identifier of the cancellation
   */
  getAnnulationById: (id: number) => Promise<AnnulerRendezVous>

  /**
   * Retrieves all cancellations for a specific appointment
   * @param id_rendez_vous - The appointment ID
   */
  getAnnulationsByRendezVous: (id_rendez_vous: number) => Promise<AnnulerRendezVous[]>

  /**
   * Creates a new appointment cancellation
   * @param annulationData - The cancellation data without ID
   */
  createAnnulation: (annulationData: Omit<AnnulerRendezVous, 'id'>) => Promise<AnnulerRendezVous>

  /**
   * Deletes a cancellation record
   * @param id - The unique identifier of the cancellation to delete
   * @throws {Error} If ID is not provided
   */
  deleteAnnulation: (id: number) => Promise<AnnulerRendezVous>
}
