import {
  administrateurs,
  Contact,
  Patient,
  Professionnel,
  ProfessionnelPatient,
  Utilisateur,
} from "@/domain/models";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { Session, User } from "@supabase/supabase-js";

export type AuthStateChangeCallback = (session: Session | null) => void;

export type SignUpProps = {
  user?: Omit<Utilisateur, "id" | "cree_a" | "mis_a_jour_a">;
  contact?: {
    numero?: string;
  }[];
  professionnelId?: number;
} & (
    | {
      role: utilisateurs_role_enum.PATIENT;
      userData: Omit<Patient, "id" | "utilisateur_id" | 'unique_id'>;
    }
    | {
      role: utilisateurs_role_enum.PROFESSIONNEL;
      userData: Omit<Professionnel, "id" | "utilisateur_id">;
    }
  );

export interface IAuthRepository {
  login: (credentials: { email: string; password: string }) => Promise<User>;

  logout: () => Promise<void>;

  signUp: (props: SignUpProps) => Promise<Patient | Professionnel>;

  getSession: () => Promise<Session | null>;

  getUser: () => Promise<User | null>;

  getAuthenticationData: () => Promise<{
    userData: Patient | administrateurs | Professionnel | null;
    user: Utilisateur;
    authData: User;
  }>;

  onAuthStateChange: (callback: AuthStateChangeCallback) => void;
  resendValidationEmail: (email: string) => Promise<string>;
}
