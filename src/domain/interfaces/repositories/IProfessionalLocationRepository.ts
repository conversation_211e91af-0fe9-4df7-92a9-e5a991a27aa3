import { EtablissementProfessionnel } from '@/domain/models'

/**
 * Interface for managing professional locations
 * Following Interface Segregation Principle by grouping related location methods
 */
export interface IProfessionalLocationRepository {
  /**
   * Retrieves a specific location by ID
   * @param id - The location ID
   */
  getLocation: (id: number) => Promise<EtablissementProfessionnel>

  /**
   * Retrieves location for a professional
   * Note: A professional can only have one location
   * @param professionalId - The professional's ID
   */
  getLocationByProfessionalId: (professionalId: number) => Promise<EtablissementProfessionnel>

  /**
   * Retrieves locations by region
   * @param region - The region name
   */
  getLocationsByRegion: (region: string) => Promise<EtablissementProfessionnel[]>

  /**
   * Retrieves locations by district
   * @param district - The district name
   */
  getLocationsByDistrict: (district: string) => Promise<EtablissementProfessionnel[]>

  /**
   * Creates a new location for a professional
   * Will not create if the professional already has a location
   * @param location - The location data without ID
   */
  createLocation: (
    location: Omit<EtablissementProfessionnel, 'id'>
  ) => Promise<EtablissementProfessionnel | undefined>

  /**
   * Updates an existing location
   * @param id - The location ID
   * @param data - The partial location data to update
   */
  updateLocation: (
    id: number,
    data: Partial<EtablissementProfessionnel>
  ) => Promise<EtablissementProfessionnel>

  /**
   * Deletes a location
   * @param id - The location ID
   */
  deleteLocation: (id: number) => Promise<void>
}
