import { ICreateProfessionalMotClesRepository } from "@/domain/interfaces/repositories/motcles";
import { ICreateProfessionalMotClesUsecase } from "@/domain/interfaces/usecases/motCles/ICreateProfessionalMotClesUsecase.ts";
import { MotClesProfessionnel } from "@/domain/models";

class CreateProfessionalMotClesUsecase implements ICreateProfessionalMotClesUsecase {
  constructor(
    private readonly createProfessionalMotClesRepository: ICreateProfessionalMotClesRepository,
  ) { }

  async execute(motcles: Omit<MotClesProfessionnel, "id">[]) {
    try {
      const motClesWithoutId: Omit<MotClesProfessionnel, "id">[] = motcles.map((motcle) => {
        return {
          symptome: motcle.symptome,
          id_professionnel: motcle.id_professionnel,
        };
      });
      return await this.createProfessionalMotClesRepository.execute(motClesWithoutId);
    } catch (error) {
      console.error("Erreur lors de la creaction des mots cles professionnels", error);
      return null;
    }
  }
}

export default CreateProfessionalMotClesUsecase;
