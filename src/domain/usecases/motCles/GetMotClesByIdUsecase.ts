import { IGetMotClesByIdRepository } from "@/domain/interfaces/repositories/motcles";
import { IGetMotClesByIdUsecase } from "@/domain/interfaces/usecases/motCles/IGetMotClesByIdUsecase.ts";

class GetMotClesByIdUsecase implements IGetMotClesByIdUsecase {
  constructor(
    private readonly getMotClesByIdRepository: IGetMotClesByIdRepository,
  ) {}

  async execute(id: number) {
    return await this.getMotClesByIdRepository.execute(id);
  }
}

export default GetMotClesByIdUsecase;
