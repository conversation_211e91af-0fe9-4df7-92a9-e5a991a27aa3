import { IGetMotClesRepository } from "@/domain/interfaces/repositories/motcles";
import { IGetMotClesUsecase } from "@/domain/interfaces/usecases/motCles/IGetMotClesUsecase.ts";

class GetMotClesUsecase implements IGetMotClesUsecase {
  constructor(private readonly getMotClesRepository: IGetMotClesRepository) {}

  execute() {
    return this.getMotClesRepository.execute();
  }
}

export default GetMotClesUsecase;
