import { IGetProfessionalMotClesRepository } from "@/domain/interfaces/repositories/motcles";
import { IGetProfessionalMotClesUsecase } from "@/domain/interfaces/usecases/motCles/IGetProfessionalMotClesUsecase.ts";

class GetProfessionalMotClesUsecase implements IGetProfessionalMotClesUsecase {
  constructor(
    private readonly getProfessionalMotClesRepository: IGetProfessionalMotClesRepository,
  ) {}

  async execute() {
    return await this.getProfessionalMotClesRepository.execute();
  }
}

export default GetProfessionalMotClesUsecase;
