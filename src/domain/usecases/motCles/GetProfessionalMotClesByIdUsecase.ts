import { IGetProfessionalMotClesByIdRepository } from "@/domain/interfaces/repositories/motcles";
import { IGetProfessionalMotClesByIdUsecase } from "@/domain/interfaces/usecases/motCles/IGetProfessionalMotClesByIdUsecase.ts";

class GetProfessionalMotClesByIdUsecase implements IGetProfessionalMotClesByIdUsecase {
  constructor(
    private readonly getProfessionalMotClesByIdRepository: IGetProfessionalMotClesByIdRepository,
  ) {}

  async execute(id: number) {
    return await this.getProfessionalMotClesByIdRepository.execute(id);
  }
}

export default GetProfessionalMotClesByIdUsecase;
