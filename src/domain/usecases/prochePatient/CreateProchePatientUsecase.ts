import { Proche } from "@/domain/models"
import { ICreateProchePatientUsecase } from "@/domain/interfaces/usecases/prochePatient"
import { ICreateProchePatientRepository } from "@/domain/interfaces/repositories/prochePatient"

export class CreateProchePatientUsecase implements ICreateProchePatientUsecase {
  constructor(private createProchePatientRepository: ICreateProchePatientRepository) { }

  async execute(patientData: Omit<Proche, "id">): Promise<Proche> {
    return await this.createProchePatientRepository.execute(patientData)
  }
}
