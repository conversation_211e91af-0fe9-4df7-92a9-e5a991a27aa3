import { Proche } from "@/domain/models"
import { IDeleteProcheUsecase } from "@/domain/interfaces/usecases/prochePatient"
import { DeleteProcheRepository } from "@/infrastructure/repositories/prochePatient/DeleteProcheRepository"

export class DeleteProcheUsecase implements IDeleteProcheUsecase {
  constructor(private deleteProcheRepository: DeleteProcheRepository) { }

  async execute(id: number): Promise<Proche> {
    return await this.deleteProcheRepository.execute(id)
  }
}
