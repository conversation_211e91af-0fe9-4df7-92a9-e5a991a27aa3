import { Proche } from "@/domain/models"
import { IGetProchePatientUsecase } from "@/domain/interfaces/usecases/prochePatient"
import { IGetProchePatientRepository } from "@/domain/interfaces/repositories/prochePatient"

export class GetProchePatientUsecase implements IGetProchePatientUsecase {
  constructor(private getProchePatientRepository: IGetProchePatientRepository) { }

  async execute(patientId: number): Promise<Proche[]> {
    return await this.getProchePatientRepository.execute(patientId)
  }
}
