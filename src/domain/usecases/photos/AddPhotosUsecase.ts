import { IAddPhotosRepository } from "@/domain/interfaces/repositories/images";
import { IAddPhotosUsecase } from "@/domain/interfaces/usecases/photos";
import { Photo } from "@/domain/models/Photo";

class AddPhotosUsecase implements IAddPhotosUsecase {
  constructor(private readonly addPhotoRepository: IAddPhotosRepository) {}

  async execute(
    profilePhoto: Omit<Photo, "id"> | Omit<Photo, "id">[]
  ): Promise<Photo[]> {
    return await this.addPhotoRepository.execute(profilePhoto);
  }
}

export default AddPhotosUsecase;
