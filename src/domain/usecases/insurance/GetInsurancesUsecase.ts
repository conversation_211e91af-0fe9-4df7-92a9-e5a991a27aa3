import { IGetInsurancesUsecase } from "@/domain/interfaces/usecases/insurance";
import { IGetInsurancesRepository } from "@/domain/interfaces/repositories/insurance";

class GetInsurancesUsecase implements IGetInsurancesUsecase {
  constructor(private getInsurancesRepository: IGetInsurancesRepository) {}

  async execute() {
    return await this.getInsurancesRepository.execute();
  }
}

export default GetInsurancesUsecase;
