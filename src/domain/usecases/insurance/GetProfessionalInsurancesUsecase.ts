import { IGetProfessionalInsurancesUsecase } from "@/domain/interfaces/usecases/insurance";
import { IGetProfessionalInsurancesRepository } from "@/domain/interfaces/repositories/insurance";

class GetProfessionalInsurancesUsecase implements IGetProfessionalInsurancesUsecase {
  constructor(private getProfessionalInsurancesRepository: IGetProfessionalInsurancesRepository) {}

  async execute(professional_id: number) {
    return await this.getProfessionalInsurancesRepository.execute(professional_id);
  }
}

export default GetProfessionalInsurancesUsecase;
