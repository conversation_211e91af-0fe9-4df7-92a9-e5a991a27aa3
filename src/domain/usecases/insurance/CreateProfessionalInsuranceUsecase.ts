import { ICreateProfessionalInsuranceRepository } from "@/domain/interfaces/repositories/insurance";
import { ICreateProfessionalInsuranceUsecase } from "@/domain/interfaces/usecases/insurance";
import { AssuranceProfessionnel } from "@/domain/models";

class CreateProfessionalInsuranceUsecase implements ICreateProfessionalInsuranceUsecase {
  constructor(
    private readonly createProfessionalInsuranceRepository: ICreateProfessionalInsuranceRepository
  ) { }

  async execute(insurance: Omit<AssuranceProfessionnel, "id">[]): Promise<AssuranceProfessionnel[]> {
    try {
      return await this.createProfessionalInsuranceRepository.execute(insurance);
    } catch (error) {

      console.error("Erreur lors de la creation des assurances professionnelles: ", error);
      return null;
    }
  }
}

export default CreateProfessionalInsuranceUsecase;
