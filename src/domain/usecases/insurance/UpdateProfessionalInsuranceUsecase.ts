import { IUpdateProfessionalInsuranceRepository } from "@/domain/interfaces/repositories/insurance";
import { IUpdateProfessionalInsuranceUsecase } from "@/domain/interfaces/usecases/insurance";
import { AssuranceProfessionnel } from "@/domain/models";

class UpdateProfessionalInsuranceUsecase implements IUpdateProfessionalInsuranceUsecase {
  constructor(
    private readonly updateProfessionalInsuranceRepository: IUpdateProfessionalInsuranceRepository
  ) {}

  async execute(id: number, insurance: Partial<AssuranceProfessionnel>): Promise<AssuranceProfessionnel | null> {
    try {
      return await this.updateProfessionalInsuranceRepository.execute(id, insurance);
    } catch (error) {
      console.error("Error updating professional insurance:", error);
      return null;
    }
  }
}

export default UpdateProfessionalInsuranceUsecase;