import { IDeleteProfessionalInsuranceRepository } from "@/domain/interfaces/repositories/insurance";
import { IDeleteProfessionalInsuranceUsecase } from "@/domain/interfaces/usecases/insurance";
import { AssuranceProfessionnel } from "@/domain/models";

class DeleteProfessionalInsuranceUsecase implements IDeleteProfessionalInsuranceUsecase {
  constructor(
    private readonly deleteProfessionalInsuranceRepository: IDeleteProfessionalInsuranceRepository
  ) {}

  async execute(id: number): Promise<AssuranceProfessionnel | null> {
    try {
      return await this.deleteProfessionalInsuranceRepository.execute(id);
    } catch (error) {
      console.error("Error deleting professional insurance:", error);
      return null;
    }
  }
}

export default DeleteProfessionalInsuranceUsecase;