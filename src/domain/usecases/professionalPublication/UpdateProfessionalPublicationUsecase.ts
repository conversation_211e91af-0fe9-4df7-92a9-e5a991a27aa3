import { IUpdateProfessionalPublicationRepository } from "@/domain/interfaces/repositories/professionalPublication";
import { IUpdateProfessionalPublicationUsecase } from "@/domain/interfaces/usecases/professionalPublication";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel";

class UpdateProfessionalPublicationUsecase implements IUpdateProfessionalPublicationUsecase {
  constructor(
    private readonly updateProfessionalPublicationRepository: IUpdateProfessionalPublicationRepository
  ) {}

  async execute(id: number, publication: Partial<PublicationProfessionnel>): Promise<PublicationProfessionnel | null> {
    try {
      return await this.updateProfessionalPublicationRepository.execute(id, publication);
    } catch (error) {
      console.error("Error updating professional publication:", error);
      return null;
    }
  }
}

export default UpdateProfessionalPublicationUsecase;