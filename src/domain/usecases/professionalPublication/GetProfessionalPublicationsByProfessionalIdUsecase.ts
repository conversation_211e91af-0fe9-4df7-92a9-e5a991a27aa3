import { IGetProfessionalPublicationsByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalPublication";
import { IGetProfessionalPublicationsByProfessionalIdUsecase } from "@/domain/interfaces/usecases/professionalPublication";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel";

class GetProfessionalPublicationsByProfessionalIdUsecase implements IGetProfessionalPublicationsByProfessionalIdUsecase {
  constructor(
    private readonly getProfessionalPublicationsByProfessionalIdRepository: IGetProfessionalPublicationsByProfessionalIdRepository
  ) {}

  async execute(professionalId: number): Promise<PublicationProfessionnel[] | null> {
    try {
      return await this.getProfessionalPublicationsByProfessionalIdRepository.execute(professionalId);
    } catch (error) {
      console.error("Error getting professional publications by professional ID:", error);
      return null;
    }
  }
}

export default GetProfessionalPublicationsByProfessionalIdUsecase;