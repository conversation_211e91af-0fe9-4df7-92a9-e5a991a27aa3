import { IDeleteProfessionalPublicationRepository } from "@/domain/interfaces/repositories/professionalPublication";
import { IDeleteProfessionalPublicationUsecase } from "@/domain/interfaces/usecases/professionalPublication";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel";

class DeleteProfessionalPublicationUsecase implements IDeleteProfessionalPublicationUsecase {
  constructor(
    private readonly deleteProfessionalPublicationRepository: IDeleteProfessionalPublicationRepository
  ) {}

  async execute(id: number): Promise<PublicationProfessionnel | null> {
    try {
      return await this.deleteProfessionalPublicationRepository.execute(id);
    } catch (error) {
      console.error("Error deleting professional publication:", error);
      return null;
    }
  }
}

export default DeleteProfessionalPublicationUsecase;