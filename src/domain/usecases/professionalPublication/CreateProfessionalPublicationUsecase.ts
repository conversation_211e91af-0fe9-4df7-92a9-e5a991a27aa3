import { ICreateProfessionalPublicationRepository } from "@/domain/interfaces/repositories/professionalPublication/ICreateProfessionalPublicationRepository.ts";
import { ICreateProfessionalPublicationUsecase } from "@/domain/interfaces/usecases/professionalPublication";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel";

class CreateProfessionalPublicationUsecase implements ICreateProfessionalPublicationUsecase {
  constructor(
    private readonly createProfessionalPublicationRepository: ICreateProfessionalPublicationRepository
  ) { }

  async execute(publication: Omit<PublicationProfessionnel, "id">[]): Promise<PublicationProfessionnel[]> {
    try {
      const publicationsWithoutId: Omit<PublicationProfessionnel, "id">[] = publication.map((pub) => {
        return {
          titre: pub.titre,
          annee: pub.annee,
          id_professionnel: pub.id_professionnel,
          description: pub.description,
          lien: pub.lien
        };
      });
      return await this.createProfessionalPublicationRepository.execute(publicationsWithoutId);
    } catch (error) {
      console.error("Erreur lors de la creation des publications professionnelles", error);
      return null;
    }
  }
}

export default CreateProfessionalPublicationUsecase;
