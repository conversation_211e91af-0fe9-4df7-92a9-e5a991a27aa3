import { IGetPatientByUserIdRepository } from "@/domain/interfaces/repositories/patients";
import { IGetPatientByUserIdUsecase } from "@/domain/interfaces/usecases/patients";
import { Patient } from "@/domain/models";

class GetPatientByUserIdUsecase implements IGetPatientByUserIdUsecase {
  constructor(private readonly repository: IGetPatientByUserIdRepository) { }
  async execute(id: number): Promise<Patient> {
    return await this.repository.execute(id);
  }
}

export default GetPatientByUserIdUsecase