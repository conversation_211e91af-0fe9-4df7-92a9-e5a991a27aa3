import { IGetPatientByIdRepository } from "@/domain/interfaces/repositories/patients";
import { IGetPatientByIdUsecase } from "@/domain/interfaces/usecases/patients";
import { Patient } from "@/domain/models";

class GetPatientByIdUsecase implements IGetPatientByIdUsecase {
  constructor(
    private readonly getPatientByIdRepository: IGetPatientByIdRepository,
  ) {}

  async execute(id: number): Promise<Patient | null> {
    return this.getPatientByIdRepository.execute(id);
  }
}

export default GetPatientByIdUsecase;

