import { IEditPatientRepository } from "@/domain/interfaces/repositories/patients"
import { IEditPatientUsecase } from "@/domain/interfaces/usecases/patients"
import { Patient } from "@/domain/models"

class EditPatientUsecase implements IEditPatientUsecase {
  constructor(private editPatientRepository: IEditPatientRepository) { }

  async execute(id: number, patientData: Partial<Patient>): Promise<Patient> {
    return await this.editPatientRepository.execute(id, patientData)
  }
}

export default EditPatientUsecase