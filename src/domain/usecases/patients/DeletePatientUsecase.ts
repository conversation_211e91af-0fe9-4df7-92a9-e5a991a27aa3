import { Patient } from "@/domain/models";
import { IDeletePatientRepository } from "../../interfaces/repositories/patients";
import { IDeletePatientUsecase } from "@/domain/interfaces/usecases/patients";

class DeletePatientUsecase implements IDeletePatientUsecase {
  constructor(private deletePatientRepository: IDeletePatientRepository) { }

  async execute(id: number): Promise<Patient> {
    return await this.deletePatientRepository.execute(id)
  }
}

export default DeletePatientUsecase