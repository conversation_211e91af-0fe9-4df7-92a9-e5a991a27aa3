import { IGetPatientsRepository } from "@/domain/interfaces/repositories/patients"
import { IGetPatientsUsecase } from "@/domain/interfaces/usecases/patients"
import { Patient } from "@/domain/models"

class GetPatientsUsecase implements IGetPatientsUsecase {
  constructor(private getPatientsRepository: IGetPatientsRepository) { }

  execute(): Promise<Patient[]> {
    return this.getPatientsRepository.execute()
  }
}

export default GetPatientsUsecase