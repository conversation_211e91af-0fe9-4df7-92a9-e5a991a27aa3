import { Patient } from "@/domain/models";
import { ICreatePatientRepository } from "../../interfaces/repositories/patients";
import { ICreatePatientUsecase } from "@/domain/interfaces/usecases/patients";

class CreatePatientUsecase implements ICreatePatientUsecase {
  constructor(private createPatientRepository: ICreatePatientRepository) {}

  async execute(patientData: Omit<Patient, "id">): Promise<Patient> {
    return await this.createPatientRepository.execute(patientData);
  }
}

export default CreatePatientUsecase;

