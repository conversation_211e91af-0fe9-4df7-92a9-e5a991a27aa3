import { IGetContactRepository } from "@/domain/interfaces/repositories/contact";
import { IGetContactUsecase } from "@/domain/interfaces/usecases/contact";
import { Contact } from "@/domain/models";

export class GetContactUsecase implements IGetContactUsecase {
  constructor(private readonly getContactRepository: IGetContactRepository) {}

  async execute(id: number): Promise<Contact[]> {
    return await this.getContactRepository.execute(id);
  }
}