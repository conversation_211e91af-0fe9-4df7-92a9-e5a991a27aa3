import { IDeleteContactRepository } from "@/domain/interfaces/repositories/contact";
import { IDeleteContactUsecase } from "@/domain/interfaces/usecases/contact";
import { Contact } from "@/domain/models";

export class DeleteContactUsecase implements IDeleteContactUsecase {
    constructor(
        private readonly deleteContactRepository: IDeleteContactRepository,
    ) { }

    async execute(
        id: number
    ): Promise<Contact | null> {
        try {
            return await this.deleteContactRepository.execute(id);
        } catch (error) {
            return null;
        }
    }
}