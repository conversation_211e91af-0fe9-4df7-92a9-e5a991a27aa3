import { IUpdateContactRepository } from "@/domain/interfaces/repositories/contact";
import { IUpdateContactUsecase } from "@/domain/interfaces/usecases/contact";
import { Contact } from "@/domain/models";

export class UpdateContactUsecase implements IUpdateContactUsecase {
    constructor(
        private readonly updateContactRepository: IUpdateContactRepository,
    ) { }

    async execute(
        id: number,
        contact: Partial<Contact>
    ): Promise<Contact | null> {
        try {
            return await this.updateContactRepository.execute(id, contact);
        } catch (error) {
            return null;
        }
    }
}