import { ICreateContactRepository } from "@/domain/interfaces/repositories/contact";
import { ICreateContactUsecase } from "@/domain/interfaces/usecases/contact";
import { Contact } from "@/domain/models";

export class CreateContactUsecase implements ICreateContactUsecase {
    constructor(
        private readonly createContactRepository: ICreateContactRepository,
    ) { }

    async execute(
        contact: Omit<Contact, 'id'>[]
    ): Promise<Contact[] | null> {
        try {
            return await this.createContactRepository.execute(contact);
        } catch (error) {
            return null;
        }
    }
}