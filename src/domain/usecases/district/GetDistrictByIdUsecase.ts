import { IGetDistrictByIdRepository } from "@/domain/interfaces/repositories/district/IGetDistrictByIdRepository";
import { IGetDistrictByIdUsecase } from "@/domain/interfaces/usecases/district";
import { District } from "@/domain/models/District";

export class GetDistrictByIdUsecase implements IGetDistrictByIdUsecase {
  constructor(private readonly repository: IGetDistrictByIdRepository) {}

  async execute(id: number): Promise<District> {
    return await this.repository.execute(id);
  }
}
