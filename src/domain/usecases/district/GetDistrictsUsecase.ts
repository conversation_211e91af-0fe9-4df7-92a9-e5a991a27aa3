import { IGetDistrictsRepository } from "@/domain/interfaces/repositories/district/IGetDistrictsRepository";
import { IGetDistrictsUsecase } from "@/domain/interfaces/usecases/district";
import { District } from "@/domain/models/District";

export class GetDistrictsUsecase implements IGetDistrictsUsecase {
  constructor(private readonly repository: IGetDistrictsRepository) {}

  async execute(): Promise<District[]> {
    return await this.repository.execute();
  }
}
