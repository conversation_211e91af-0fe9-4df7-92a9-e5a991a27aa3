import { ISearchDistrictsByNameRepository } from "@/domain/interfaces/repositories/district";
import { ISearchDistrictsByNameUsecase } from "@/domain/interfaces/usecases/district";

class SearchDistrictsByNameUsecase implements ISearchDistrictsByNameUsecase {
  constructor(
    private readonly searchDistrictsByNameRepository: ISearchDistrictsByNameRepository
  ) {}

  async execute(searchTerm: string) {
    const data = await this.searchDistrictsByNameRepository.execute(searchTerm);

    return data;
  }
}

export default SearchDistrictsByNameUsecase;
