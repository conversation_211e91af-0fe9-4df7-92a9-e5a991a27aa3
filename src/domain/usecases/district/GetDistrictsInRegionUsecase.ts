import { IGetDistrictsInRegionRepository } from "@/domain/interfaces/repositories/district/IGetDistrictsInRegionRepository";
import { IGetDistrictsInRegionUsecase } from "@/domain/interfaces/usecases/district/IGetDistrictsInRegionUsecase";
import { District } from "@/domain/models/District";

export class GetDistrictsInRegionUsecase
  implements IGetDistrictsInRegionUsecase
{
  constructor(private readonly repository: IGetDistrictsInRegionRepository) {}

  async execute(regionId: number): Promise<District[]> {
    return await this.repository.execute(regionId);
  }
}
