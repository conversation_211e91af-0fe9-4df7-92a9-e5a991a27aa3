import { ICancelAppointmentByProfessionalRepository } from "@/domain/interfaces/repositories/appointment";
import { ICancelAppointmentByProfessionalUsecase } from "@/domain/interfaces/usecases/appointment";
import { AnnulerRendezVous } from "@/domain/models/AnnulerRendezVous";

export class CancelAppointmentByProfessionalUsecase implements ICancelAppointmentByProfessionalUsecase {
  constructor(private readonly cancelAppointmentByProfessionalRepository: ICancelAppointmentByProfessionalRepository) { }

  async execute(appointmentData: Omit<AnnulerRendezVous, "id">): Promise<AnnulerRendezVous> {
    return this.cancelAppointmentByProfessionalRepository.execute(appointmentData);
  }
}
