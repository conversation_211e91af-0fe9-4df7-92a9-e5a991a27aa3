import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { ICancelAppointmentRepository } from "@/domain/interfaces/repositories/appointment";
import { ICancelAppointmentEmailService } from "@/domain/interfaces/services/customEmailService/ICancelAppointmentEmailService";
import { ICancelAppointmentUsecase } from "@/domain/interfaces/usecases/appointment";
import { IGetPatientByIdUsecase } from "@/domain/interfaces/usecases/patients";
import { IGetProfessionalByIdUsecase } from "@/domain/interfaces/usecases/professionals/IGetProfessionalByIdUsecase";
import { IGetUserByIdUsecase } from "@/domain/interfaces/usecases/user";
import { RendezVous } from "@/domain/models";

class CancelAppointmentUsecase implements ICancelAppointmentUsecase {
  constructor(
    private readonly cancelAppointmentRepository: ICancelAppointmentRepository,
    private readonly getUserByIdUsecase: IGetUserByIdUsecase,
    private readonly getPatientIdByIdUsecase: IGetPatientByIdUsecase,
    private readonly getProfessionalByIdUsecase: IGetProfessionalByIdUsecase,
    private readonly cancelAppointmentEmailService: ICancelAppointmentEmailService
  ) {}

  async execute(appointment: AppointmentProfessionalDTO): Promise<RendezVous> {
    try {
      // Recuperation des donnees neccesaires
      const appointmentDate = String(appointment.date_rendez_vous).split(
        "T"
      )[0];
      const appointmentTime = String(appointment.date_rendez_vous).split(
        "T"
      )[1];

      const matchingProfessional =
        await this.getProfessionalByIdUsecase.execute(
          appointment.id_professionnel
        );

      const matchingPatient = await this.getPatientIdByIdUsecase.execute(
        appointment.patient.id
      );

      const appUrl = import.meta.env.VITE_APP_URL;

      const matchingUser = await this.getUserByIdUsecase.execute(
        matchingPatient?.utilisateur_id
      );

      const result = await this.cancelAppointmentRepository.execute(
        appointment.id
      );

      if (matchingUser) {
        await this.cancelAppointmentEmailService.execute({
          to: matchingUser.email,
          AppointmentDate: appointmentDate,
          AppointmentTime: appointmentTime,
          DoctorName: `${matchingProfessional.nom} ${matchingProfessional.prenom}`,
          FirstName: appointment.patient.nom,
          RescheduleURL: appUrl,
        });
      }

      return result;
    } catch (error) {
      console.log(error);

      throw error;
    }
  }
}

export default CancelAppointmentUsecase;
