import { ICompleteAppointmentRepository } from "@/domain/interfaces/repositories/appointment";
import { RendezVous } from "@/domain/models";

export class CompleteAppointmentUsecase implements ICompleteAppointmentRepository {
  constructor(private readonly completeAppointmentRepository: ICompleteAppointmentRepository) { }

  async execute(id: number): Promise<RendezVous> {
    return this.completeAppointmentRepository.execute(id);
  }
}
