import { IMarkAppointmentAsDoneRepository } from "@/domain/interfaces/repositories/appointment";
import { RendezVous } from "@/domain/models";

class MarkAppointmentAsDoneUsecase implements IMarkAppointmentAsDoneRepository {
  constructor(
    private readonly markAppointmentAsDoneRepository: IMarkAppointmentAsDoneRepository
  ) { }

  async execute(id: number): Promise<RendezVous> {
    return this.markAppointmentAsDoneRepository.execute(id);
  }
}

export default MarkAppointmentAsDoneUsecase