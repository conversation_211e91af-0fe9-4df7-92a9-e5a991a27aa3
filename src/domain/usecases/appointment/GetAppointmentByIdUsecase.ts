import { IGetAppointmentByIdRepository } from "@/domain/interfaces/repositories/appointment";
import { IGetAppointmentByIdUsecase } from "@/domain/interfaces/usecases/appointment";
import { RendezVous } from "@/domain/models";

class GetAppointmentByIdUsecase implements IGetAppointmentByIdUsecase {
  constructor(
    private readonly getAppointmentByIdRepository: IGetAppointmentByIdRepository
  ) { }

  async execute(id: number): Promise<RendezVous> {
    return this.getAppointmentByIdRepository.execute(id);
  }
}

export default GetAppointmentByIdUsecase