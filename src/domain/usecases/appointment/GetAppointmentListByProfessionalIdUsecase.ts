import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { IGetAppointmentListByProfessionalIdRepository } from "@/domain/interfaces/repositories/appointment";
import { IGetAppointmentListByProfessionalIdUsecase } from "@/domain/interfaces/usecases/appointment";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { format } from "date-fns";

export class GetAppointmentListByProfessionalIdUsecase
  implements IGetAppointmentListByProfessionalIdUsecase
{
  constructor(
    private readonly getAppointmentListByProfessionalIdRepository: IGetAppointmentListByProfessionalIdRepository
  ) {}

  async execute(id: number): Promise<AppointmentProfessionalDTO[]> {
    const appointments =
      await this.getAppointmentListByProfessionalIdRepository.execute(id);
    if (!appointments) return [];

    return appointments.map((appointment) => {
      const appointmentDate = new Date(appointment.date_rendez_vous);
      const statut =
        appointmentDate < new Date() &&
        appointment.statut === rendez_vous_statut_enum.A_VENIR
          ? rendez_vous_statut_enum.MANQUER
          : appointment.statut;

      return {
        id: appointment.id,
        time: format(appointmentDate, "HH:mm"),
        date_rendez_vous: appointment.date_rendez_vous,
        statut: statut,
        id_professionnel: appointment.id_professionnel,
        motif: appointment.motif,
        raison: appointment.raison,
        categorie: appointment.categorie,
        patient: appointment.patient,
      };
    });
  }
}
