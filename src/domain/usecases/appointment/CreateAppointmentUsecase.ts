import { ICreateAppointmentRepository } from "@/domain/interfaces/repositories/appointment";
import { ICreateAppointmentUsecase } from "@/domain/interfaces/usecases/appointment";
import { RendezVous } from "@/domain/models";
import { IConfirmAppointmentEmailService } from "@/domain/interfaces/services/customEmailService/IConfirmAppointmentEmailService";
import { IGetUserByIdUsecase } from "@/domain/interfaces/usecases/user";
import { IGetProfessionalByIdUsecase } from "@/domain/interfaces/usecases/professionals/IGetProfessionalByIdUsecase";
import { IGetPatientByIdUsecase } from "@/domain/interfaces/usecases/patients";

class CreateAppointmentUsecase implements ICreateAppointmentUsecase {
  constructor(
    private readonly createAppointmentRepository: ICreateAppointmentRepository,
    private readonly confirmAppointmentEmailService:
      IConfirmAppointmentEmailService,
    private readonly getPatientByIdUsecase: IGetPatientByIdUsecase,
    private readonly getUserByIdUsecase: IGetUserByIdUsecase,
    private readonly getProfessionalByIdUsecase: IGetProfessionalByIdUsecase,
  ) {}

  async execute(appointment: Omit<RendezVous, "id">): Promise<RendezVous> {
    // Créer le rendez-vous
    const createdAppointment = await this.createAppointmentRepository.execute(
      appointment,
    );

    try {
      // Récupérer les informations du patient
      const patient = await this.getPatientByIdUsecase.execute(
        appointment.patient_id,
      );

      const user = await this.getUserByIdUsecase.execute(
        patient.utilisateur_id,
      );

      // Récupérer les informations du professionnel
      const professional = await this.getProfessionalByIdUsecase.execute(
        appointment.id_professionnel,
      );

      if (patient && professional) {
        // Formater la date et l'heure
        const appointmentDate = new Date(appointment.date_rendez_vous);
        const formattedDate = appointmentDate.toLocaleDateString("fr-FR", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
        const formattedTime = appointmentDate.toLocaleTimeString("fr-FR", {
          hour: "2-digit",
          minute: "2-digit",
        });

        // Envoyer l'email de confirmation
        await this.confirmAppointmentEmailService.execute({
          to: user.email,
          FirstName: patient.prenom || "Patient",
          DoctorName: `${professional.titre} ${professional.nom}`,
          AppointmentDate: formattedDate,
          AppointmentTime: formattedTime,
          Location: professional.adresse || "Adresse non spécifiée",
        });
      }
    } catch (error) {
      console.error(
        "Erreur lors de l'envoi de l'email de confirmation:",
        error,
      );
      // On ne rejette pas l'erreur ici car le rendez-vous a été créé avec succès
      // L'échec de l'envoi de l'email ne doit pas empêcher la création du rendez-vous
    }

    return createdAppointment;
  }
}

export default CreateAppointmentUsecase;
