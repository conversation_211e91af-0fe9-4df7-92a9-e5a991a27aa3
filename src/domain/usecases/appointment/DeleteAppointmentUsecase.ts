import { IDeleteAppointmentRepository } from "@/domain/interfaces/repositories/appointment";
import { IDeleteAppointmentUsecase } from "@/domain/interfaces/usecases/appointment";
import { RendezVous } from "@/domain/models";

class DeleteAppointmentUsecase implements IDeleteAppointmentUsecase {
  constructor(
    private readonly deleteAppointmentRepository: IDeleteAppointmentRepository
  ) { }

  async execute(id: number): Promise<RendezVous> {
    return this.deleteAppointmentRepository.execute(id);
  }
}

export default DeleteAppointmentUsecase