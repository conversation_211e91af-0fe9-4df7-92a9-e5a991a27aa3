import { IUpdateAppointmentRepository } from "@/domain/interfaces/repositories/appointment";
import { IUpdateAppointmentUsecase } from "@/domain/interfaces/usecases/appointment";
import { RendezVous } from "@/domain/models";

class UpdateAppointmentUsecase implements IUpdateAppointmentUsecase {
  constructor(
    private readonly updateAppointmentRepository: IUpdateAppointmentRepository
  ) { }

  async execute(id: number, appointmentData: Partial<RendezVous>): Promise<RendezVous> {
    return this.updateAppointmentRepository.execute(id, appointmentData);
  }
}

export default UpdateAppointmentUsecase