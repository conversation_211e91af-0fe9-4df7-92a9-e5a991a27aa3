import { IPostponeAppointmentRepository } from "@/domain/interfaces/repositories/appointment";
import { IPostponeAppointmentUsecase } from "@/domain/interfaces/usecases/appointment";
import { RendezVous } from "@/domain/models";

export class PostponeAppointmentUsecase implements IPostponeAppointmentUsecase {
  constructor(private readonly postponeAppointmentRepository: IPostponeAppointmentRepository) { }

  async execute(id: number, dateRendezVous: Date): Promise<RendezVous> {
    return this.postponeAppointmentRepository.execute(id, dateRendezVous);
  }
}
