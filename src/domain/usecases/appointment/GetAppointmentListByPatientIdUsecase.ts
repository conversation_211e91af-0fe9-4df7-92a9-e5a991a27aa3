import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import { IGetAppointmentListByPatientIdRepository } from "@/domain/interfaces/repositories/appointment";
import { IGetAppointmentListByPatientIdUsecase } from "@/domain/interfaces/usecases/appointment/IGetAppointmentListByPatientIdUsecase";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { format } from "date-fns";

export class GetAppointmentListByPatientIdUsecase
  implements IGetAppointmentListByPatientIdUsecase
{
  constructor(
    private readonly getAppointmentListByPatientIdRepository: IGetAppointmentListByPatientIdRepository
  ) {}

  async execute(id: number): Promise<AppointmentPatientDTO[]> {
    const appointments =
      await this.getAppointmentListByPatientIdRepository.execute(id);
    if (!appointments) return [];

    return appointments.map((appointment) => {
      const appointmentDate = new Date(appointment.date_rendez_vous);
      const professional = appointment.professional;
      const statut =
        appointmentDate < new Date() &&
        appointment.statut === rendez_vous_statut_enum.A_VENIR
          ? rendez_vous_statut_enum.MANQUER
          : appointment.statut;

      return {
        id: appointment.id,
        date_rendez_vous: appointmentDate.toISOString(),
        time: format(appointmentDate, "HH:mm"),
        statut: statut,
        patient_id: appointment.patient_id,
        motif: appointment.motif,
        raison: appointment.raison,
        professional: {
          id: professional.id,
          nom: professional.nom,
          prenom: professional.prenom,
          titre: professional.titre,
          raison_sociale: professional.raison_sociale || "",
          adresse: professional.adresse,
          fokontany: professional.fokontany || "",
          commune: professional.commune || "",
          avatar: professional.avatar || "",
        },
      };
    });
  }
}
