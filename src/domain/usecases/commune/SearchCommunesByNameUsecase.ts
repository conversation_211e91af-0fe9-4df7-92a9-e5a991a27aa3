import { ISearchCommunesByNameRepository } from "@/domain/interfaces/repositories/commune";
import { ISearchCommunesByNameUsecase } from "@/domain/interfaces/usecases/commune";

class SearchCommunesByNameUsecase implements ISearchCommunesByNameUsecase {
  constructor(
    private readonly searchCommunesByNameRepository: ISearchCommunesByNameRepository
  ) {}

  async execute(searchTerm: string) {
    const data = await this.searchCommunesByNameRepository.execute(searchTerm);

    return data;
  }
}

export default SearchCommunesByNameUsecase;
