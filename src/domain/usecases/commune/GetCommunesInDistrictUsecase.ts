import { IGetCommunesInDistrictRepository } from "@/domain/interfaces/repositories/commune/IGetCommunesInDistrictRepository";
import { IGetCommunesInDistrictUsecase } from "@/domain/interfaces/usecases/commune/IGetCommunesInDistrictUsecase";
import { Commune } from "@/domain/models/Commune";

export class GetCommunesInDistrictUsecase
  implements IGetCommunesInDistrictUsecase
{
  constructor(private readonly repository: IGetCommunesInDistrictRepository) {}

  async execute(districtId: number): Promise<Commune[]> {
    return await this.repository.execute(districtId);
  }
}
