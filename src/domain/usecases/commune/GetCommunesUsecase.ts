import { IGetCommunesRepository } from "@/domain/interfaces/repositories/commune/IGetCommunesRepository";
import { IGetCommunesUsecase } from "@/domain/interfaces/usecases/commune/IGetCommunesUsecase";
import { Commune } from "@/domain/models/Commune";

export class GetCommunesUsecase implements IGetCommunesUsecase {
  constructor(private readonly repository: IGetCommunesRepository) {}

  async execute(): Promise<Commune[]> {
    return await this.repository.execute();
  }
}
