import { IGetCommuneByIdRepository } from "@/domain/interfaces/repositories/commune/IGetCommuneByIdRepository";
import { IGetCommuneByIdUsecase } from "@/domain/interfaces/usecases/commune/IGetCommuneByIdUsecase";
import { Commune } from "@/domain/models/Commune";

export class GetCommuneByIdUsecase implements IGetCommuneByIdUsecase {
  constructor(private readonly repository: IGetCommuneByIdRepository) {}

  async execute(id: number): Promise<Commune> {
    return await this.repository.execute(id);
  }
}
