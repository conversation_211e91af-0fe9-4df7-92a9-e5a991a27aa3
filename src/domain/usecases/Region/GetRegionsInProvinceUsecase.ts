import { IGetRegionsInProvinceRepository } from "@/domain/interfaces/repositories/region";
import { IGetRegionsInProvinceUsecase } from "@/domain/interfaces/usecases/region";
import { Region } from "@/domain/models";

class GetRegionsInProvinceUsecase implements IGetRegionsInProvinceUsecase {
  constructor(private readonly regionRepository: IGetRegionsInProvinceRepository) { }

  async execute(id_province: number): Promise<Region[]> {
    return await this.regionRepository.execute(id_province);
  }
}

export default GetRegionsInProvinceUsecase