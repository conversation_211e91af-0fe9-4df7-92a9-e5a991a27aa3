import { IGetRegionByIdRepository } from "@/domain/interfaces/repositories/region";
import { IGetRegionByIdUsecase } from "@/domain/interfaces/usecases/region";
import { Region } from "@/domain/models";

class GetRegionByIdUsecase implements IGetRegionByIdUsecase {
  constructor(private readonly regionRepository: IGetRegionByIdRepository) { }

  async execute(id: number): Promise<Region> {
    return await this.regionRepository.execute(id);
  }
}

export default GetRegionByIdUsecase