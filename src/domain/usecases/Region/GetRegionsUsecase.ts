import { IGetRegionsRepository } from "@/domain/interfaces/repositories/region";
import { IGetRegionsUsecase } from "@/domain/interfaces/usecases/region";
import { Region } from "@/domain/models";

class GetRegionsUsecase implements IGetRegionsUsecase {
  constructor(private readonly regionRepository: IGetRegionsRepository) { }

  async execute(): Promise<Region[]> {
    return await this.regionRepository.execute();
  }
}

export default GetRegionsUsecase