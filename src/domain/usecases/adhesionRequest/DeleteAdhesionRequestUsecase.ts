import { IDeleteAdhesionRequestRepository } from "@/domain/interfaces/repositories/adhesionRequest";
import { IDeleteAdhesionRequestUsecase } from "@/domain/interfaces/usecases/adhesionRequest";
import { demande_adhesion } from "@/domain/models";

export class DeleteAdhesionRequestUsecase
  implements IDeleteAdhesionRequestUsecase
{
  constructor(
    private readonly deleteAdhesionRequestRepository: IDeleteAdhesionRequestRepository
  ) {}

  async execute(id: number): Promise<demande_adhesion> {
    try {
      /*
      * Etapes:
        - Verifier si l'utilisateur courant a le droit de faire cela (administrateur ?)
        - Supprimer la demande d'adhesion
       */
      return await this.deleteAdhesionRequestRepository.execute(id);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
