import { IMarkAdhesionRequestAsReadRepository } from "@/domain/interfaces/repositories/adhesionRequest";
import { IMarkAdhesionRequestAsReadUsecase } from "@/domain/interfaces/usecases/adhesionRequest";
import { demande_adhesion } from "@/domain/models";

export class MarkAdhesionRequestAsReadUsecase
  implements IMarkAdhesionRequestAsReadUsecase
{
  constructor(
    private readonly markAdhesionRequestAsReadRepository: IMarkAdhesionRequestAsReadRepository
  ) {}

  async execute(id: number): Promise<demande_adhesion> {
    try {
      return await this.markAdhesionRequestAsReadRepository.execute(id);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
