import {
  IApproveAdhesionRequestRepository,
  IGetAdhesionRequestByIdRepository,
} from "@/domain/interfaces/repositories/adhesionRequest";
import { IApproveAdhesionRequestUsecase } from "@/domain/interfaces/usecases/adhesionRequest";
import { ICreateProfessionalInvitationRepository } from "@/domain/interfaces/repositories/professionalInvitation";
import { IMembershipAcceptedEmailService } from "@/domain/interfaces/services/customEmailService/IMembershipAcceptedEmailService";
import { demande_adhesion, Invitation, Utilisateur } from "@/domain/models";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { v4 as uuidv4 } from "uuid";

export class ApproveAdhesionRequestUsecase
  implements IApproveAdhesionRequestUsecase
{
  constructor(
    private readonly approveAdhesionRequestRepository: IApproveAdhesionRequestRepository,
    private readonly getAdhesionRequestByIdRepository: IGetAdhesionRequestByIdRepository,
    private readonly createProfessionalInvitationRepository: ICreateProfessionalInvitationRepository,
    private readonly membershipAcceptedEmailService: IMembershipAcceptedEmailService
  ) {}

  async execute(
    id: number,
    connectedUser: Utilisateur
  ): Promise<demande_adhesion> {
    try {
      // Vérification du statut de l'utilisateur
      if (!connectedUser) {
        throw new Error("Utilisateur non connecté");
      }

      if (!connectedUser.role) {
        throw new Error("Rôle de l'utilisateur non défini");
      }

      if (connectedUser.role !== utilisateurs_role_enum.ADMIN) {
        throw new Error(
          "Vous n'avez pas les droits pour effectuer cette action"
        );
      }

      // Récupérer la demande d'adhésion complète
      const adhesionRequest =
        await this.getAdhesionRequestByIdRepository.execute(id);

      if (!adhesionRequest) {
        throw new Error("Demande d'adhésion non trouvée");
      }

      // Générer un token unique
      const token = uuidv4();

      // Créer une invitation pour le professionnel
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 7); // Expire dans 7 jours

      // Formater les dates pour Supabase (ISO string)
      const invitation: Omit<Invitation, "id"> = {
        id_demande_adhesion: adhesionRequest.id,
        token: token,
        expire_le: expirationDate.toISOString(),
        est_utilisee: false,
        cree_par: connectedUser.id,
        cree_le: new Date().toISOString(),
      };

      // Sauvegarder l'invitation dans la base de données
      await this.createProfessionalInvitationRepository.execute(invitation);

      // Envoyer un email d'acceptation d'adhésion avec le lien d'inscription
      await this.membershipAcceptedEmailService.execute({
        to: adhesionRequest.email,
        FirstName: adhesionRequest.prenom,
        token: token,
      });

      // Mettre à jour le statut de la demande d'adhésion à "approuvée"
      return await this.approveAdhesionRequestRepository.execute(id);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
