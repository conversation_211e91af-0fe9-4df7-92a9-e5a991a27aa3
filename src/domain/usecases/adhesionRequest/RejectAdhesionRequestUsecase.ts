import { IRejectAdhesionRequestRepository } from "@/domain/interfaces/repositories/adhesionRequest";
import { IRejectAdhesionRequestUsecase } from "@/domain/interfaces/usecases/adhesionRequest";
import { demande_adhesion } from "@/domain/models";

export class RejectAdhesionRequestUsecase
  implements IRejectAdhesionRequestUsecase
{
  constructor(
    private readonly rejectAdhesionRequestRepository: IRejectAdhesionRequestRepository
  ) {}

  async execute(id: number): Promise<demande_adhesion> {
    try {
      return await this.rejectAdhesionRequestRepository.execute(id);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
