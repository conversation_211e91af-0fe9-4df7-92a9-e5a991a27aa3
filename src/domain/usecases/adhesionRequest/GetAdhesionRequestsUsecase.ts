import { IGetAdhesionRequestsRepository } from "@/domain/interfaces/repositories/adhesionRequest";
import { IGetAdhesionRequestsUsecase } from "@/domain/interfaces/usecases/adhesionRequest";
import { demande_adhesion } from "@/domain/models";

export class GetAdhesionRequestsUsecase implements IGetAdhesionRequestsUsecase {
  constructor(
    private readonly getAdhesionRequestsRepository: IGetAdhesionRequestsRepository
  ) {}

  async execute(): Promise<demande_adhesion[]> {
    try {
      return await this.getAdhesionRequestsRepository.execute();
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
