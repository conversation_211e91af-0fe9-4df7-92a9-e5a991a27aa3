import { IGetUserByEmailUsecase } from "@/domain/interfaces/usecases/user";
import { IGetAdhesionRequestByEmailRepository } from "@/domain/interfaces/repositories/adhesionRequest";
import { ICheckEmailValidity } from "@/domain/interfaces/usecases/AdhesionRequest/ICheckEmailValidity";
import { EmailValidator } from "@/domain/services/EmailValidator";

export class CheckEmailValidity extends EmailValidator implements ICheckEmailValidity {
  constructor(
    private readonly getUserByEmailUsecase: IGetUserByEmailUsecase,
    private readonly getAdhesionRequestByEmailRepository: IGetAdhesionRequestByEmailRepository
  ) {
    super();
  }

  async isEmailValid(email: string): Promise<boolean> {
    const isValid = this.isValid(email);

    if (!isValid) {
      return Promise.resolve(false);
    }

    const matchingUser = await this.getUserByEmailUsecase.execute(email);

    if (matchingUser) {
      return false
    }

    const matchingAdhesionRequest = await this.getAdhesionRequestByEmailRepository.execute(email);

    if (matchingAdhesionRequest) {
      return false
    }

    return true
  }
}
