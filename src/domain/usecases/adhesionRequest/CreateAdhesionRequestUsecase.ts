import { ICreateAdhesionRequestRepository } from "@/domain/interfaces/repositories/adhesionRequest";
import { ICheckEmailValidity } from "@/domain/interfaces/usecases/AdhesionRequest/ICheckEmailValidity";
import { ICreateAdhesionRequestUsecase } from "@/domain/interfaces/usecases/ICreateAdhesionRequestUsecase";
import { demande_adhesion } from "@/domain/models";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";

export class CreateAdhesionRequestUsecase
  implements ICreateAdhesionRequestUsecase {
  constructor(
    private readonly createAdhesionRequestRepository:
      ICreateAdhesionRequestRepository,
    private readonly checkEmailValidity: ICheckEmailValidity,
  ) {}

  async execute(
    adhesionData: Omit<demande_adhesion, "id" | "status" | "cree_a">,
  ): Promise<demande_adhesion> {
    try {
      const isEmailStillValid = await this.checkEmailValidity.isEmailValid(
        adhesionData.email,
      );

      if (!isEmailStillValid) {
        throw new Error(ErrorMessages.ADHESION_REQUEST_EMAIL_NOT_AVAILABLE);
      }

      return await this.createAdhesionRequestRepository.execute(adhesionData);
    } catch (error) {
      throw new Error(error);
    }
  }
}
