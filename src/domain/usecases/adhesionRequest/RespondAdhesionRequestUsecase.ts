import { ICreateProfessionalInvitationRepository } from "@/domain/interfaces/repositories/professionalInvitation";
import {
  IMarkAdhesionRequestAsReadUsecase,
  IRespondAdhesionRequestUsecase,
} from "@/domain/interfaces/usecases/adhesionRequest";
import { IMembershipAcceptedEmailService } from "@/domain/interfaces/services/customEmailService/IMembershipAcceptedEmailService";
import { demande_adhesion, Invitation, Utilisateur } from "@/domain/models";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { v4 as uuidv4 } from "uuid";

class RespondAdhesionRequestUsecase implements IRespondAdhesionRequestUsecase {
  constructor(
    private readonly markAdhesionRequestAsReadUsecase: IMarkAdhesionRequestAsReadUsecase,
    private readonly createProfessionalInvitationRepository: ICreateProfessionalInvitationRepository,
    private readonly membershipAcceptedEmailService: IMembershipAcceptedEmailService
  ) {}

  async execute(adhesionRequest: demande_adhesion, connectedUser: Utilisateur) {
    /*
    * Traitement:
      - Vérifier si l'utilisateur connecté est autorisé à faire cette opération
      - Générer une URL qui permettra au professionnel de créer son compte
      - Coder cette URL en utilisant JWT
      - Sauvegarder les données de cette URL dans la table "invitations" pour
          permettre la vérification du côté frontend
      - Insérer les données dans la table "invitation"
      - Envoyer un email
      - Si tout se passe bien, mettre à jour l'état de la demande d'adhésion
    */

    // Vérification du statut de l'utilisateur
    if (!connectedUser) {
      throw new Error("Utilisateur non connecté");
    }

    if (!connectedUser.role) {
      throw new Error("Rôle de l'utilisateur non défini");
    }

    if (connectedUser.role !== utilisateurs_role_enum.ADMIN) {
      throw new Error("Vous n'avez pas les droits pour effectuer cette action");
    }

    // Générer un token unique
    const token = uuidv4();

    // Créer une invitation pour le professionnel
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + 7); // Expire dans 7 jours

    // Formater les dates pour Supabase (ISO string)
    const invitation: Omit<Invitation, "id"> = {
      id_demande_adhesion: adhesionRequest.id,
      token: token,
      expire_le: expirationDate.toISOString(),
      est_utilisee: false,
      cree_par: connectedUser.id,
      cree_le: new Date().toISOString(),
    };

    // Sauvegarder l'invitation dans la base de données
    await this.createProfessionalInvitationRepository.execute(invitation);

    // Envoyer un email d'acceptation d'adhésion avec le lien d'inscription
    await this.membershipAcceptedEmailService.execute({
      to: adhesionRequest.email,
      FirstName: adhesionRequest.prenom,
      token: token,
    });

    // Marquer la demande d'adhésion comme lue
    const result = await this.markAdhesionRequestAsReadUsecase.execute(
      adhesionRequest.id
    );

    return result;
  }
}

export { RespondAdhesionRequestUsecase };
