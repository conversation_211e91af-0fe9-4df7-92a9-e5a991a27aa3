import { ordre_appartenance } from "@/domain/models";
import { IEditOrdreAppartenanceRepository } from "@/domain/interfaces/repositories/ordreAppartenance/IEditOrdreAppartenanceRepository";
import { IEditOrdreAppartenanceUsecase } from "@/domain/interfaces/usecases/ordreAppartenance";

class EditOrdreAppartenanceUsecase implements IEditOrdreAppartenanceUsecase {
  constructor(
    private readonly editOrdreAppartenanceRepository:
      IEditOrdreAppartenanceRepository,
  ) {}

  async execute(
    id: number,
    ordreAppartenanceData: Partial<ordre_appartenance>,
  ): Promise<ordre_appartenance> {
    return this.editOrdreAppartenanceRepository.execute(
      id,
      ordreAppartenanceData,
    );
  }
}

export default EditOrdreAppartenanceUsecase;
