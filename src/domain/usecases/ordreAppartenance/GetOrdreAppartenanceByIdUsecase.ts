import { ordre_appartenance } from "@/domain/models";
import { IGetOrdreAppartenanceByIdRepository } from "@/domain/interfaces/repositories/ordreAppartenance/IGetOrdreAppartenanceByIdRepository";
import { IGetOrdreAppartenanceByIdUsecase } from "@/domain/interfaces/usecases/ordreAppartenance";

class GetOrdreAppartenanceByIdUsecase
  implements IGetOrdreAppartenanceByIdUsecase {
  constructor(
    private readonly getOrdreAppartenanceByIdRepository:
      IGetOrdreAppartenanceByIdRepository,
  ) {}

  async execute(id: number): Promise<ordre_appartenance> {
    return this.getOrdreAppartenanceByIdRepository.execute(id);
  }
}

export default GetOrdreAppartenanceByIdUsecase;
