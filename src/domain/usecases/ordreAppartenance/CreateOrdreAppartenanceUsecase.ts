import { ordre_appartenance_professionnel } from "@/domain/models";
import { ICreateOrdreAppartenanceRepository } from "@/domain/interfaces/repositories/ordreAppartenance/ICreateOrdreAppartenanceRepository";
import { ICreateOrdreAppartenanceUsecase } from "@/domain/interfaces/usecases/ordreAppartenance";

class CreateOrdreAppartenanceUsecase
  implements ICreateOrdreAppartenanceUsecase {
  constructor(
    private readonly createOrdreAppartenanceRepository:
      ICreateOrdreAppartenanceRepository,
  ) { }

  async execute(
    ordreAppartenanceInformations: Omit<ordre_appartenance_professionnel, "id">[],
  ): Promise<ordre_appartenance_professionnel[]> {

    return this.createOrdreAppartenanceRepository.execute(
      ordreAppartenanceInformations
    );
  }
}

export default CreateOrdreAppartenanceUsecase;
