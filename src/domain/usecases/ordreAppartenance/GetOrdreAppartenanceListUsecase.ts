import { ordre_appartenance } from "@/domain/models";
import { IGetOrdreAppartenanceListRepository } from "@/domain/interfaces/repositories/ordreAppartenance/IGetOrdreAppartenanceListRepository";
import { IGetOrdreAppartenanceListUsecase } from "@/domain/interfaces/usecases/ordreAppartenance";

class GetOrdreAppartenanceListUsecase
  implements IGetOrdreAppartenanceListUsecase {
  constructor(
    private readonly getOrdreAppartenanceListRepository:
      IGetOrdreAppartenanceListRepository,
  ) {}

  async execute(): Promise<ordre_appartenance[]> {
    return this.getOrdreAppartenanceListRepository.execute();
  }
}

export default GetOrdreAppartenanceListUsecase;
