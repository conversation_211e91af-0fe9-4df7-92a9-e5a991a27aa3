import { ordre_appartenance } from "@/domain/models";
import { IDeleteOrdreAppartenanceRepository } from "@/domain/interfaces/repositories/ordreAppartenance/IDeleteOrdreAppartenanceRepository";
import { IDeleteOrdreAppartenanceUsecase } from "@/domain/interfaces/usecases/ordreAppartenance";

class DeleteOrdreAppartenanceUsecase
  implements IDeleteOrdreAppartenanceUsecase {
  constructor(
    private readonly deleteOrdreAppartenanceRepository:
      IDeleteOrdreAppartenanceRepository,
  ) {}

  async execute(id: number): Promise<ordre_appartenance> {
    return this.deleteOrdreAppartenanceRepository.execute(id);
  }
}

export default DeleteOrdreAppartenanceUsecase;
