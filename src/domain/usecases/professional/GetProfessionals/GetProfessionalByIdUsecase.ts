import { ProfessionalProfileData } from "@/domain/DTOS"
import { IGetProfessionalByIdRepository } from "@/domain/interfaces/repositories/professionals/IGetProfessionalByIdRepository"
import { IGetProfessionalByIdUsecase } from "@/domain/interfaces/usecases/professionals/IGetProfessionalByIdUsecase"

export class GetProfessionalByIdUsecase implements IGetProfessionalByIdUsecase {
  constructor(
    private readonly getProfessionalByIdRepository: IGetProfessionalByIdRepository
  ) { }
  async execute(id: number): Promise<ProfessionalProfileData> {
    return this.getProfessionalByIdRepository.execute(id)
  }
}
