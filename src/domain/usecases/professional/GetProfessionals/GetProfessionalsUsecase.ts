import { IGetProfessionalsRepository } from "@/domain/interfaces/repositories/professionals/IGetProfessionalsRepository"
import { IGetProfessionalsUsecase } from "@/domain/interfaces/usecases/professionals/IGetProfessionalsUsecase"
import { ProfessionalCompleteDTO } from "@/domain/DTOS"

class GetProfessionalsUsecase implements IGetProfessionalsUsecase {
  constructor(
    private readonly getProfessionalsRepository: IGetProfessionalsRepository
  ) { }
  async execute(): Promise<ProfessionalCompleteDTO[]> {
    return this.getProfessionalsRepository.execute()
  }
}

export default GetProfessionalsUsecase