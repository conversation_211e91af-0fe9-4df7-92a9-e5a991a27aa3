import { ICreateEventRepository, IDeleteEventByProfessionalIdRepository } from "@/domain/interfaces/repositories/evenement";
import { IGenerateRecurringEvents } from "@/domain/interfaces/services/IGenerateRecurringEvents";
import { Evenement } from "@/domain/models";
import { addYears } from "date-fns";

export class UpdateEventByProfessionalIdUsecase {
    constructor(
        private readonly deleteEventByProfessionalIdRepository: IDeleteEventByProfessionalIdRepository,
        private readonly createEventRepository: ICreateEventRepository,
        private readonly generateRecurringEvents: IGenerateRecurringEvents
    ) { }

    async execute(
        id: number,
        data: Omit<Evenement, 'id'>
    ): Promise<Evenement[] | null> {
        try {
            await this.deleteEventByProfessionalIdRepository.execute(id);
            const recurringEvents = this.generateRecurringEvents.generate(data, new Date(data.date_debut), addYears(new Date(data.date_debut), 1));

            return await this.createEventRepository.execute(recurringEvents);
        } catch (error) {
            return null;
        }
    }
}
