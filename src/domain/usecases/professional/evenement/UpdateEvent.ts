import { IUpdateEventRepository } from "@/domain/interfaces/repositories/evenement/IUpdateEventRepository";
import { Evenement } from "@/domain/models";

export class UpdateEventUsecase {
    constructor(
        private readonly updateEventRepository: IUpdateEventRepository,
    ) { }

    async execute(
        id: number,
        evenement: Partial<Evenement>
    ): Promise<Evenement | null> {
        try {
            return await this.updateEventRepository.execute(id, evenement);
        } catch (error) {
            return null;
        }
    }
}
