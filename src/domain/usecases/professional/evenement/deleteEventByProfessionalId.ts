import { IDeleteEventByProfessionalIdRepository } from "@/domain/interfaces/repositories/evenement";
import { Evenement } from "@/domain/models";

export class DeleteEventByProfessionalIdUsecase {
    constructor(
        private readonly deleteEventByProfessionalIdRepository: IDeleteEventByProfessionalIdRepository,
    ) { }

    async execute(
        id: number
    ): Promise<Evenement[] | null> {
        try {
            return await this.deleteEventByProfessionalIdRepository.execute(id);
        } catch (error) {
            return null;
        }
    }
}
