import { ICreateEventRepository } from "@/domain/interfaces/repositories/evenement";
import { IGenerateRecurringEvents } from "@/domain/interfaces/services/IGenerateRecurringEvents";
import { Evenement } from "@/domain/models";
import { addYears } from 'date-fns';

export class CreateEventsUsecase {
    constructor(
        private readonly createEventRepository: ICreateEventRepository,
        private readonly generateRecurringEvents: IGenerateRecurringEvents,
    ) { }

    async execute(
        evenement: Omit<Evenement, 'id'>
    ): Promise<Evenement[] | null> {
        try {
            const recurringEvents = this.generateRecurringEvents.generate(evenement, new Date(evenement.date_debut), addYears(new Date(evenement.date_debut), 1));
            return await this.createEventRepository.execute(recurringEvents);
        } catch (error) {
            return null;
        }
    }
}
