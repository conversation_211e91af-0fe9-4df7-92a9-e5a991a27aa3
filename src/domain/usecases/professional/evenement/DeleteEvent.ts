import { IDeleteEventRepository } from "@/domain/interfaces/repositories/evenement";
import { Evenement } from "@/domain/models";

export class DeleteEventUsecase {
    constructor(
        private readonly deleteEventRepository: IDeleteEventRepository,
    ) { }

    async execute(
        id: number
    ): Promise<Evenement | null> {
        try {
            return await this.deleteEventRepository.execute(id);
        } catch (error) {
            return null;
        }
    }
}
