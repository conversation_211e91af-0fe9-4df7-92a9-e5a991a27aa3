import { IGetEventsByProfessionalIdRepository } from "@/domain/interfaces/repositories/evenement/IGetEventsByProfessionalIdRepository";
import { Evenement } from "@/domain/models";

export class GetEventsByProfessionalIdUsecase {
    constructor(
        private readonly getEventsByProfessionalIdRepository: IGetEventsByProfessionalIdRepository,
    ) { }

    async execute(
        professionalId: number
    ): Promise<Evenement[] | null> {
        try {
            const events = await this.getEventsByProfessionalIdRepository.execute(professionalId);
            return events;
        } catch (error) {
            return null;
        }
    }
}
