import { searchProfessionalByIdParams } from "./types";
import { ProfessionalCardDTO, TimeSlotProffessionalCard } from "@/domain/DTOS";
import { IProfessionalAvailabilitiesFilter } from "@/domain/interfaces/services/IProfessionalAvaiabilitiesFilter";
import { IGetProfessionalInformationByIdRepository } from "@/domain/interfaces/repositories/searchProfessionals/ISearchProfessionalsByIdRepository";
import { ISearchProfessionalByIdUsecase } from "@/domain/interfaces/usecases/searchProfessinals/ISearchProfessionalByIdRepository";
import { IGetContactRepository } from "@/domain/interfaces/repositories/contact";

export class SearchProfessionalByIdUsecase
  implements ISearchProfessionalByIdUsecase
{
  constructor(
    private readonly getProfessionalInformationRepository: IGetProfessionalInformationByIdRepository,
    private readonly professionalAvailabilitiesFilter: IProfessionalAvailabilitiesFilter,
    private readonly getContactRepository: IGetContactRepository
  ) {}

  async execute(searchParams: searchProfessionalByIdParams) {
    try {
      const data = await this.getProfessionalInformationRepository.execute({
        id: searchParams.id,
        today: searchParams.today,
      });

      if (!data) return null;

      if (data.parametre_disponibilite.length === 0) {
        return {
          ...data,
          disponibilite: [],
          specialite: data.specialites_professionnel,
        };
      }

      const formattedAvailalities: TimeSlotProffessionalCard[] =
        this.professionalAvailabilitiesFilter.filter(
          data.parametre_disponibilite[0],
          data.rendez_vous,
          data.evenement,
          data.parametre_disponibilite[0].temps_moyen_consulation
        );

      const contacts = await this.getContactRepository.execute(
        data.utilisateur_id
      );

      const out: ProfessionalCardDTO = {
        ...data,
        disponibilite: formattedAvailalities,
        specialite: data.specialites_professionnel,
        horaire_hebdomadaire:
          data.parametre_disponibilite[0].horaire_hebdomadaire,
        contacts: contacts,
      };

      return out;
    } catch (error) {
      console.log("error", error);
    }
  }
}
