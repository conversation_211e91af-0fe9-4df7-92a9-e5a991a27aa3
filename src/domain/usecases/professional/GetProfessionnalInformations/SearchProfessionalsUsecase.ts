import { IGetProfessionalInformationRepository } from "@/domain/interfaces/repositories/ISearchProfessionalsRepository";
import { ISearchProfessionalsUsecase } from "@/domain/interfaces/usecases/IGetProfessionalInformationsUsecase";
import { searchProfessionalsParams } from "./types";
import { ProfessionalCardDTO, TimeSlotProffessionalCard } from "@/domain/DTOS";
import { IProfessionalAvailabilitiesFilter } from "@/domain/interfaces/services/IProfessionalAvaiabilitiesFilter";

export class SearchProfessionalsUsecase implements ISearchProfessionalsUsecase {
  constructor(
    private readonly getProfessionalInformationRepository: IGetProfessionalInformationRepository,
    private readonly professionalAvailabilitiesFilter: IProfessionalAvailabilitiesFilter
  ) {}

  async execute(searchParams: searchProfessionalsParams) {
    try {
      const searchKey = {
        ...searchParams,
        name: searchParams.name.split(" ")[0],
      };

      const data = await this.getProfessionalInformationRepository.execute({
        name: searchKey.name || "",
        localization: searchKey.localization || "",
        today: searchKey.today,
      });

      // Filtrage
      const test = data.filter((d) => {
        const specialities = d.specialites_professionnel;
        const etablishments = d.etablissements_professionnel;
        const motCles = d.mot_cles;
        const nameSplited = searchParams.name.split(" ");

        const matchingSpecialities = specialities.filter((speciality) =>
          speciality.nom_specialite
            .toLowerCase()
            .includes(searchParams.name.toLowerCase())
        );

        const matchingEtablishemts = etablishments.filter((etablishment) =>
          etablishment.nom_etablissement
            .toLowerCase()
            .includes(searchParams.name.toLowerCase())
        );

        const matchingName =
          (d.nom.includes(nameSplited[0]) &&
            d.prenom.includes(nameSplited[1])) ||
          (d.nom.includes(nameSplited[1]) && d.prenom.includes(nameSplited[0]));

        const matchingMotCles = motCles.filter((motCle) =>
          motCle.symptome
            .toLowerCase()
            .includes(searchParams.name.toLowerCase())
        );

        return (
          matchingSpecialities.length > 0 ||
          matchingEtablishemts.length > 0 ||
          matchingMotCles.length > 0 ||
          !!matchingName
        );
      });

      if (!test || test.length == 0) return [];

      const formattedData: ProfessionalCardDTO[] = test.map((professional) => {
        if (professional.parametre_disponibilite.length === 0) {
          return {
            ...professional,
            disponibilite: [],
            specialite: professional.specialites_professionnel,
          };
        }
        const formattedAvailalities: TimeSlotProffessionalCard[] =
          this.professionalAvailabilitiesFilter.filter(
            professional.parametre_disponibilite[0],
            professional.rendez_vous,
            professional.evenement,
            professional.parametre_disponibilite[0].temps_moyen_consulation
          );

        return {
          ...professional,
          disponibilite: formattedAvailalities,
          specialite: professional.specialites_professionnel,
        };
      });

      return formattedData;
    } catch (error) {
      console.log("error", error);
    }
  }
}
