import { IUpdateCarnetSanteRepository } from '@/domain/interfaces/repositories/carnetSante'
import { CarnetSante } from '@/domain/models'

export class UpdateCarnetSanteUseCase {
  constructor(
    private readonly updateCarnetSanteRepository: IUpdateCarnetSanteRepository
  ) {}

  async execute(id: number, data: Partial<CarnetSante>): Promise<CarnetSante> {
    return await this.updateCarnetSanteRepository.execute(id, data)
  }
}
