import { ICreateCarnetSanteRepository } from '@/domain/interfaces/repositories/carnetSante'
import { CarnetSante } from '@/domain/models'

export class CreateCarnetSanteUseCase {
  constructor(
    private readonly createCarnetSanteRepository: ICreateCarnetSanteRepository
  ) {}

  async execute(data: Omit<CarnetSante, "id">): Promise<CarnetSante> {
    return await this.createCarnetSanteRepository.execute(data)
  }
}
