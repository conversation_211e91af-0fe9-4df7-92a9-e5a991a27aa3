import { Antecedant_sociaux } from '@/domain/models'
import { ICreateAntecedentSociauxRepository } from '@/domain/interfaces/repositories/antecedentSociaux'

export class CreateAntecedentSociauxUseCase {
  constructor(
    private readonly createAntecedentSociauxRepository: ICreateAntecedentSociauxRepository
  ) {}

  async execute(data: Antecedant_sociaux): Promise<Antecedant_sociaux> {
    return await this.createAntecedentSociauxRepository.create(data)
  }
}
