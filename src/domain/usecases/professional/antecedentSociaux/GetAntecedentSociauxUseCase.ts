import { Antecedant_sociaux } from '@/domain/models'
import { IGetAntecedentSociauxRepository } from '@/domain/interfaces/repositories/antecedentSociaux'

export class GetAntecedentSociauxUseCase {
  constructor(
    private readonly getAntecedentSociauxRepository: IGetAntecedentSociauxRepository
  ) {}

  async getById(id: number): Promise<Antecedant_sociaux | null> {
    return await this.getAntecedentSociauxRepository.getById(id)
  }

  async getAll(carnetId: number): Promise<Antecedant_sociaux[]> {
    return await this.getAntecedentSociauxRepository.getAll(carnetId)
  }
}
