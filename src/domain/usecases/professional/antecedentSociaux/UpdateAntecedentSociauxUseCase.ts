import { Antecedant_sociaux } from '@/domain/models'
import { IUpdateAntecedentSociauxRepository } from '@/domain/interfaces/repositories/antecedentSociaux'

export class UpdateAntecedentSociauxUseCase {
  constructor(
    private readonly updateAntecedentSociauxRepository: IUpdateAntecedentSociauxRepository
  ) {}

  async execute(id: number, data: Partial<Antecedant_sociaux>): Promise<Antecedant_sociaux> {
    return await this.updateAntecedentSociauxRepository.update(id, data)
  }
}
