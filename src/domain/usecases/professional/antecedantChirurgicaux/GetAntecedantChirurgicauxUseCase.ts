import { AntecedantChirurgicaux } from '@/domain/models'
import { IGetAntecedantChirurgicauxRepository } from '@/domain/interfaces/repositories/antecedantChirurgicaux'

export class GetAntecedantChirurgicauxUseCase {
  constructor(
    private readonly getAntecedantChirurgicauxRepository: IGetAntecedantChirurgicauxRepository
  ) {}

  async getById(id: number): Promise<AntecedantChirurgicaux | null> {
    return await this.getAntecedantChirurgicauxRepository.getById(id)
  }

  async getAll(carnetId: number): Promise<AntecedantChirurgicaux[]> {
    return await this.getAntecedantChirurgicauxRepository.getAll(carnetId)
  }
}
