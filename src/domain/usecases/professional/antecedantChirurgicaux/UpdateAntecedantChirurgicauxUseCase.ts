import { AntecedantChirurgicaux } from '@/domain/models'
import { IUpdateAntecedantChirurgicauxRepository } from '@/domain/interfaces/repositories/antecedantChirurgicaux'

export class UpdateAntecedantChirurgicauxUseCase {
  constructor(
    private readonly updateAntecedantChirurgicauxRepository: IUpdateAntecedantChirurgicauxRepository
  ) {}

  async execute(id: number, data: Partial<AntecedantChirurgicaux>): Promise<AntecedantChirurgicaux> {
    const antecedantChirurgicaux = await this.updateAntecedantChirurgicauxRepository.update(id, data)
    return{
      ...antecedantChirurgicaux,
      date: antecedantChirurgicaux.date instanceof Date ? new Date(antecedantChirurgicaux.date).toISOString() : antecedantChirurgicaux.date,
    }
  }
}
