import { AntecedantChirurgicaux } from '@/domain/models'
import { ICreateAntecedantChirurgicauxRepository } from '@/domain/interfaces/repositories/antecedantChirurgicaux'

export class CreateAntecedantChirurgicauxUseCase {
  constructor(
    private readonly createAntecedantChirurgicauxRepository: ICreateAntecedantChirurgicauxRepository
  ) {}

  async execute(data: Omit<AntecedantChirurgicaux, "id">[]): Promise<AntecedantChirurgicaux[]> {
    const antecedantChirurgicaux = await this.createAntecedantChirurgicauxRepository.create(data)
    const result = antecedantChirurgicaux.map((r) => {
      return{
        ...r,
        date: r.date instanceof Date ? new Date(r.date).toISOString() : r.date,
      }
    })
    return result
  }
}
