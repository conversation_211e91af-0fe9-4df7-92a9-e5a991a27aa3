import { IDeleteAntecedantChirurgicauxRepository } from '@/domain/interfaces/repositories/antecedantChirurgicaux'

export class DeleteAntecedantChirurgicauxUseCase {
  constructor(
    private readonly deleteAntecedantChirurgicauxRepository: IDeleteAntecedantChirurgicauxRepository
  ) {}

  async execute(id: number): Promise<void> {
    await this.deleteAntecedantChirurgicauxRepository.delete(id)
  }
}
