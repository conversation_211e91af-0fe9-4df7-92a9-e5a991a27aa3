import { AntecedantFamilliaux } from '@/domain/models'
import { IGetAntecedantFamiliauxRepository } from '@/domain/interfaces/repositories/antecedantFamiliaux'

export class GetAntecedantFamiliauxUseCase {
  constructor(
    private readonly getAntecedantFamiliauxRepository: IGetAntecedantFamiliauxRepository
  ) {}

  async getById(id: number): Promise<AntecedantFamilliaux | null> {
    return await this.getAntecedantFamiliauxRepository.getById(id)
  }

  async getAll(carnetId: number): Promise<AntecedantFamilliaux[]> {
    return await this.getAntecedantFamiliauxRepository.getAll(carnetId)
  }
}
