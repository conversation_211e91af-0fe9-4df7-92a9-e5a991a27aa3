import { AntecedantFamilliaux } from '@/domain/models'
import { ICreateAntecedantFamiliauxRepository } from '@/domain/interfaces/repositories/antecedantFamiliaux'

export class CreateAntecedantFamiliauxUseCase {
  constructor(
    private readonly createAntecedantFamiliauxRepository: ICreateAntecedantFamiliauxRepository
  ) {}

  async execute(data: Omit<AntecedantFamilliaux, "id">[]): Promise<AntecedantFamilliaux[]> {
    return await this.createAntecedantFamiliauxRepository.create(data)
  }
}
