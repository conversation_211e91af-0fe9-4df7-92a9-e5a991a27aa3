import { AntecedantFamilliaux } from '@/domain/models'
import { IUpdateAntecedantFamiliauxRepository } from '@/domain/interfaces/repositories/antecedantFamiliaux'

export class UpdateAntecedantFamiliauxUseCase {
  constructor(
    private readonly updateAntecedantFamiliauxRepository: IUpdateAntecedantFamiliauxRepository
  ) {}

  async execute(id: number, data: Partial<AntecedantFamilliaux>): Promise<AntecedantFamilliaux> {
    return await this.updateAntecedantFamiliauxRepository.update(id, data)
  }
}
