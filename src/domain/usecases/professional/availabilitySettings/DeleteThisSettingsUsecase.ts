import { IDeleteSpecificDateScheduleRepository } from "@/domain/interfaces/repositories/specificDataSchedule";
import { IHandleExceptions } from "@/domain/interfaces/services/IHandleExceptions";
import { horaire_date_specifique } from "@/domain/models";

export class DeleteThisSettingsUsecase {
    constructor(
        private readonly deleteSpecificDateScheduleRepository: IDeleteSpecificDateScheduleRepository,
        private readonly handleExceptionsService: IHandleExceptions
    ) { }

    async execute(
        id_parametre_disponibilite: number,
        exceptions: Omit<horaire_date_specifique, "id">[],
    ): Promise<horaire_date_specifique[] | null> {
        try {
            // supprimer l'anciennes exceptions
            await this.deleteSpecificDateScheduleRepository.execute(id_parametre_disponibilite);
            await this.handleExceptionsService.execute(exceptions, id_parametre_disponibilite);
            return exceptions;
        } catch (error) {
            return null;
        }
    }
}
