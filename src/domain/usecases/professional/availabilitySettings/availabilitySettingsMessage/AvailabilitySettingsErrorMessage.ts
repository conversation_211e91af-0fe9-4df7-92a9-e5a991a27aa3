export class AvailabilitySettingsErrorMessage {
    static readonly SETTINGS_FETCHED =
        'Une erreur est survenue lors de la recherche des paramètres de disponibilité';
    static readonly SETTINGS_FETCHED_BY_ID =
        'Une erreur est survenue lors de la recherche des paramètres de disponibilité';
    static readonly SETTINGS_CREATED =
        'Une erreur est survenue lors de la création des paramètres de disponibilité';
    static readonly SETTINGS_UPDATED =
        'Une erreur est survenue lors de la mise à jour des paramètres de disponibilité';
    static readonly SETTINGS_DELETED =
        'Une erreur est survenue lors de la suppression des paramètres de disponibilité';
}