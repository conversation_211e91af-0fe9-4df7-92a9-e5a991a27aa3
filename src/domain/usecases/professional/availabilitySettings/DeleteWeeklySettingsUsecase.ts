import { IDeleteWeeklyScheduleRepository } from "@/domain/interfaces/repositories/weeklySchedule";
import { IHandleWeeklySchedule } from "@/domain/interfaces/services/IHandleWeeklySchedule";
import { horaire_hebdomadaire } from "@/domain/models";

export class DeleteWeeklySettingsUsecase {
    constructor(
        private readonly deleteWeeklyScheduleRepository: IDeleteWeeklyScheduleRepository,
        private readonly handleWeeklyScheduleService: IHandleWeeklySchedule,
    ) { }

    async execute(
        settingsId: number,
        schedules: Omit<horaire_hebdomadaire, "id">[],
    ): Promise<horaire_hebdomadaire[] | null> {
        try {
            await this.deleteWeeklyScheduleRepository.execute(settingsId);
            await this.handleWeeklyScheduleService.execute({ horaire_hebdomadaire: schedules }, settingsId);
            return schedules;
        } catch (error) {
            return null;
        }
    }
}
