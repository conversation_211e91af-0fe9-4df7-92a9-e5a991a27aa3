import { AvailabilitySettingsDTO } from "@/domain/DTOS/";
import { IGetSettingsByProfessionalIdRepository } from "@/domain/interfaces/repositories/availabilitySettings/IGetSettingsByProfessionalIdRepository";
import { DEFAULT_HORAIRE_HEBDOMADAIRE } from "@/shared/constants/DefaultHoraireHebdomadaire";

export class GetSettingsByProfessionalIdUsecase {
    constructor(
        private readonly getSettingsByProfessionalIdRepository: IGetSettingsByProfessionalIdRepository,
    ) { }

    async execute(
        professionalId: number
    ): Promise<AvailabilitySettingsDTO | null> {
        try {
            const settings =
                await this.getSettingsByProfessionalIdRepository.execute(professionalId)

            if (!settings) {
                return null;
            }

            const transformedData: AvailabilitySettingsDTO = {
                id: settings.id,
                id_professionnel: settings.id_professionnel,
                type: settings.type,
                date_debut: settings.date_debut instanceof Date ? settings.date_debut.toISOString() : settings.date_debut,
                date_fin: settings.date_fin instanceof Date ? settings.date_fin.toISOString() : settings.date_fin,
                duree_pause: settings.duree_pause,
                max_rdv_par_jours: settings.max_rdv_par_jours,
                peut_inviter_autre: settings.peut_inviter_autre,
                temps_moyen_consulation: settings.temps_moyen_consulation,
                horaire_hebdomadaire: settings.horaire_hebdomadaire.length ? settings.horaire_hebdomadaire.map((horaire) => ({
                    id: horaire.id,
                    jour: horaire.jour,
                    creneau_horaire: horaire.creneau_horaire || []
                })) : DEFAULT_HORAIRE_HEBDOMADAIRE,
                horaire_date_specifique: settings.horaire_date_specifique || [],
                pauses: settings.pauses || []
            }

            return transformedData
        } catch (error) {
            return null;
        }
    }
}
