import { IUpdateSettingsRepository } from "@/domain/interfaces/repositories/availabilitySettings/IUpdateSettingsRepository";
import { parametre_disponibilite } from "@/domain/models";

export class UpdateSettingsUsecase {
    constructor(
        private readonly updateSettingsRepository: IUpdateSettingsRepository,
    ) { }

    async execute(
        id: number,
        settings: Partial<parametre_disponibilite>,
    ): Promise<parametre_disponibilite | null> {
        try {
            return await this.updateSettingsRepository.execute(id, settings);
        } catch (error) {
            return null;
        }
    }
}
