import { AvailabilitySettingsDTO } from "@/domain/DTOS/";
import { parametre_disponibilite } from "@/domain/models";
import { IHandleWeeklySchedule } from "@/domain/interfaces/services/IHandleWeeklySchedule";
import { IHandleSpecificSchedule } from "@/domain/interfaces/services/IHandleSpecificSchedule";
import { ICreateSettingsRepository } from "@/domain/interfaces/repositories/availabilitySettings/ICreateSettingsRepository";
import { IDeleteSettingsByProfessionalIdRepository } from "@/domain/interfaces/repositories/availabilitySettings/IDeleteSettingsByProfessionalIdRepository";

export class CreateSettingsUsecase {
    constructor(
        private readonly createSettingsRepository: ICreateSettingsRepository,
        private readonly deleteSettingsByProfessionalId: IDeleteSettingsByProfessionalIdRepository,
        private readonly handleWeeklyScheduleService: IHandleWeeklySchedule,
        private readonly handleSpecificScheduleService: IHandleSpecificSchedule,
    ) { }

    async execute(
        data: AvailabilitySettingsDTO
    ): Promise<AvailabilitySettingsDTO | null> {
        try {
            const professionalId = data.id_professionnel;

            // 1. Delete old settings
            await this.deleteSettingsByProfessionalId.execute(professionalId);

            // 2. Create new base settings
            const setting: Omit<parametre_disponibilite, "id"> = {
                id_professionnel: data.id_professionnel,
                type: data.type,
                date_debut: data.date_debut,
                date_fin: data.date_fin,
                duree_pause: data.duree_pause,
                max_rdv_par_jours: data.max_rdv_par_jours,
                peut_inviter_autre: data.peut_inviter_autre,
                temps_moyen_consulation: data.temps_moyen_consulation
            };
            const settingsData = await this.createSettingsRepository.execute(setting);

            // 3. Handle schedule types in parallel
            const schedulePromises: Promise<void>[] = [];

            // Handle weekly schedule
            if (data.type === 'hebdomadaire' && data.horaire_hebdomadaire) {
                schedulePromises.push(this.handleWeeklyScheduleService.execute({ horaire_hebdomadaire: data.horaire_hebdomadaire, horaire_date_specifique: data.horaire_date_specifique }, settingsData.id));
            }

            // Handle specific dates
            if (data.type === 'specifique' && data.horaire_date_specifique) {
                schedulePromises.push(this.handleSpecificScheduleService.execute(data.horaire_date_specifique, settingsData.id));
            }

            // Handle pauses

            // Wait for all schedule operations to complete
            await Promise.all(schedulePromises);

            return data;
        } catch (error) {
            console.error('Error in CreateSettingsUsecase:', error);
            throw error;
        }
    }
}
