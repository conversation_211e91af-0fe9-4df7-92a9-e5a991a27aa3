import { AvailabilitySettingsDTO } from "@/domain/DTOS";
import { IDeleteSettingsByProfessionalIdRepository } from "@/domain/interfaces/repositories/availabilitySettings/IDeleteSettingsByProfessionalIdRepository";

export class DeleteSettingsUsecase {
    constructor(
        private readonly deleteSettingsByProfessionalId: IDeleteSettingsByProfessionalIdRepository,
    ) { }

    async execute(
        id: number
    ): Promise<AvailabilitySettingsDTO | null> {
        try {
            const data = await this.deleteSettingsByProfessionalId.execute(id);
            return data;
        } catch (error) {
            return null;
        }
    }
}
