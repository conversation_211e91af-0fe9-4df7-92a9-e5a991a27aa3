import { AffectationMedical } from '@/domain/models'
import { ICreateAffectationMedicaleRepository } from '@/domain/interfaces/repositories/affectationMedicale'

export class CreateAffectationMedicaleUseCase {
  constructor(
    private readonly createAffectationMedicaleRepository: ICreateAffectationMedicaleRepository
  ) {}

  async execute(data: Omit<AffectationMedical, "id">[]): Promise<AffectationMedical[]> {
    const affectationMedicale = await this.createAffectationMedicaleRepository.create(data)
    const result = affectationMedicale.map((r) => {
      return{
        ...r,
        date: r.date instanceof Date ? new Date(r.date).toISOString() : r.date,
      }
    })
    return result
  }
}
