import { AffectationMedical } from '@/domain/models'
import { IGetAffectationMedicaleRepository } from '@/domain/interfaces/repositories/affectationMedicale'

export class GetAffectationMedicaleUseCase {
  constructor(
    private readonly getAffectationMedicaleRepository: IGetAffectationMedicaleRepository
  ) {}

  async getById(id: number): Promise<AffectationMedical | null> {
    return await this.getAffectationMedicaleRepository.getById(id)
  }

  async getAll(carnetId: number): Promise<AffectationMedical[]> {
    return await this.getAffectationMedicaleRepository.getAll(carnetId)
  }
}
