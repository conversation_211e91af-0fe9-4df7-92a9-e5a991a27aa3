import { AffectationMedical } from '@/domain/models'
import { IUpdateAffectationMedicaleRepository } from '@/domain/interfaces/repositories/affectationMedicale'

export class UpdateAffectationMedicaleUseCase {
  constructor(
    private readonly updateAffectationMedicaleRepository: IUpdateAffectationMedicaleRepository
  ) {}

  async execute(id: number, data: Partial<AffectationMedical>): Promise<AffectationMedical> {
    const affectationMedicale = await this.updateAffectationMedicaleRepository.update(id, data)
    return{
      ...affectationMedicale,
      date: affectationMedicale.date instanceof Date ? new Date(affectationMedicale.date).toISOString() : affectationMedicale.date,
    }
  }
}
