import { IGetDiagnosticByIdRepository } from '@/domain/interfaces/repositories/diagnostic/IGetDiagnosticByIdRepository'
import { laboratoire_diagnostics } from '@/domain/models'

export class GetDiagnosticByIdUsecase {
  constructor(
    private readonly getDiagnosticByIdRepository: IGetDiagnosticByIdRepository
  ) {}

  async getById(id: number): Promise<laboratoire_diagnostics | null> {
    return await this.getDiagnosticByIdRepository.execute(id)
  }
}
