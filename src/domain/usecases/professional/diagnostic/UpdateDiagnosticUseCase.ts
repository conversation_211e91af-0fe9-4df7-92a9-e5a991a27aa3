import { laboratoire_diagnostics } from '@/domain/models'
import { IUpdateDiagnosticRepository } from '@/domain/interfaces/repositories/diagnostic'
import { IDeleteImageService } from '@/domain/interfaces/services/IDeleteImageService'
import { IUploadService } from '@/domain/interfaces/services/IUploadServices';
import { upload_file_enum } from '@/domain/models/enums';

export class UpdateDiagnosticUseCase {
  constructor(
    private readonly deleteImageService: IDeleteImageService,
    private readonly uploadServices: IUploadService,
    private readonly updateDiagnosticRepository: IUpdateDiagnosticRepository
  ) {}

  async execute(id: number, data: Partial<laboratoire_diagnostics>, selectedFile: File | null): Promise<laboratoire_diagnostics> {
    // Si le logo est modifié, supprimer l'ancien logo
    if (data.path) {
      try {
        await this.deleteImageService.execute(data.path);
        await this.uploadServices.execute(selectedFile, upload_file_enum.diagnostics);
      } catch (error) {
        throw new Error(error)
      }
    }

    return await this.updateDiagnosticRepository.execute(id, data)
  }
}
