import { IGetAllDiagnosticRepository } from '@/domain/interfaces/repositories/diagnostic'
import { laboratoire_diagnostics } from '@/domain/models'

export class GetAllDiagnosticUseCase {
  constructor(
    private readonly getAllDiagnosticRepository: IGetAllDiagnosticRepository
  ) {}
  
  async getAll(carnetId: number): Promise<laboratoire_diagnostics[]> {
    return await this.getAllDiagnosticRepository.execute(carnetId)
  }
}
