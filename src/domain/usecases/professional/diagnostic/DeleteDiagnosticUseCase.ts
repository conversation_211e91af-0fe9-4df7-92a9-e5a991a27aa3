import { IDeleteDiagnosticRepository, IGetOldPathRepository } from '@/domain/interfaces/repositories/diagnostic'
import { IDeleteImageService } from '@/domain/interfaces/services/IDeleteImageService'

export class DeleteDiagnosticUseCase {
  constructor(
    private readonly getOldFileNameRepository: IGetOldPathRepository,
    private readonly deleteDiagnosticRepository: IDeleteDiagnosticRepository,
    private readonly deleteImageService: IDeleteImageService,
  ) {}

  async execute(id: number): Promise<void> {
    const oldPath = await this.getOldFileNameRepository.execute(id);
    await this.deleteDiagnosticRepository.execute(id)
    if (oldPath) {
      try {
        await this.deleteImageService.execute(oldPath);
      } catch (error) {
        throw new Error(error)
      }
    }
  }
}
