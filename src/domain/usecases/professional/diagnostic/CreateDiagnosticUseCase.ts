import { ICreateDiagnosticRepository } from '@/domain/interfaces/repositories/diagnostic'
import { laboratoire_diagnostics } from '@/domain/models'

export class CreateDiagnosticUseCase {
  constructor(
    private readonly createDiagnosticRepository: ICreateDiagnosticRepository
  ) {}

  async execute(data: Omit<laboratoire_diagnostics, "id">): Promise<laboratoire_diagnostics> {
    return await this.createDiagnosticRepository.execute(data)
  }
}
