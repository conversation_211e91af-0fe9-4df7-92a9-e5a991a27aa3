import { ICreateFacturationRepository } from "@/domain/interfaces/repositories/facturation";
import { Facturation } from "@/domain/models";

export class CreateFacturationUsecase {
  constructor(
    private readonly createFacturationRepository: ICreateFacturationRepository
  ) {}

  async execute(
    consultation: Omit<Facturation, "id">
  ): Promise<Facturation | null> {
    return await this.createFacturationRepository.execute(consultation);
  }
}
