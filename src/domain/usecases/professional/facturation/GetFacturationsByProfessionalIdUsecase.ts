import { IGetFacturationsByProfessionalIdRepository } from "@/domain/interfaces/repositories/facturation";
import { FacturationDTO } from "@/domain/DTOS";

export class GetFacturationsByProfessionalIdUsecase {
  constructor(
    private readonly getFacturationsByProfessionalIdRepository: IGetFacturationsByProfessionalIdRepository
  ) {}

  async execute(professional_id: number): Promise<FacturationDTO[] | null> {
    return await this.getFacturationsByProfessionalIdRepository.execute(
      professional_id
    );
  }
}
