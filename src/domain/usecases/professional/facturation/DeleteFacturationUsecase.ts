import { IDeleteFacturationRepository } from "@/domain/interfaces/repositories/facturation";
import { Facturation } from "@/domain/models";

export class DeleteFacturationUsecase {
  constructor(
    private readonly deleteFacturationRepository: IDeleteFacturationRepository
  ) {}

  async execute(id: number): Promise<Facturation | null> {
    return await this.deleteFacturationRepository.execute(id);
  }
}
