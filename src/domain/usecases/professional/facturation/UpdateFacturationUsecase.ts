import { IUpdateFacturationRepository } from "@/domain/interfaces/repositories/facturation";
import { Facturation } from "@/domain/models";

export class UpdateFacturationUsecase {
  constructor(
    private readonly updateFacturationRepository: IUpdateFacturationRepository
  ) {}

  async execute(
    id: number,
    facturation: Partial<Facturation>
  ): Promise<Facturation | null> {
    return await this.updateFacturationRepository.execute(id, facturation);
  }
}
