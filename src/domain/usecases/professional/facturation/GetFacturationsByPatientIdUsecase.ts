import { FacturationDTO } from "@/domain/DTOS";
import { IGetFacturationByPatientIdRepository } from "@/domain/interfaces/repositories/facturation";

export class GetFacturationByPatientIdUsecase {
  constructor(
    private readonly getFacturationByPatientIdRepository: IGetFacturationByPatientIdRepository
  ) {}

  async execute(patient_id: number): Promise<FacturationDTO[] | null> {
    return await this.getFacturationByPatientIdRepository.execute(patient_id);
  }
}
