import { Allergie } from '@/domain/models'
import { IUpdateAllergieRepository } from '@/domain/interfaces/repositories/allergie'

export class UpdateAllergieUseCase {
  constructor(
    private readonly updateAllergieRepository: IUpdateAllergieRepository
  ) {}

  async execute(id: number, data: Partial<Allergie>): Promise<Allergie> {
    return await this.updateAllergieRepository.execute(id, data)
  }
}
