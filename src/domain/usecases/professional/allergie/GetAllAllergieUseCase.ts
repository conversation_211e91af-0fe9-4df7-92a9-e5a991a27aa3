import { IGetAllAllergieRepository } from '@/domain/interfaces/repositories/allergie/IGetAllAllergieRepository'
import { Allergie } from '@/domain/models'

export class GetAllAllergieUseCase {
  constructor(
    private readonly getAllAllergieRepository: IGetAllAllergieRepository
  ) {}
  
  async getAll(carnetId: number): Promise<Allergie[]> {
    return await this.getAllAllergieRepository.execute(carnetId)
  }
}
