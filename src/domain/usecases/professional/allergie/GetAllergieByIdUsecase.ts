import { IGetAllergieByIdRepository } from '@/domain/interfaces/repositories/allergie/IGetAllergieByIdRepository'
import { Allergie } from '@/domain/models'

export class GetAllergieByIdUsecase {
  constructor(
    private readonly getAllergieByIdRepository: IGetAllergieByIdRepository
  ) {}

  async getById(id: number): Promise<Allergie | null> {
    return await this.getAllergieByIdRepository.execute(id)
  }
}
