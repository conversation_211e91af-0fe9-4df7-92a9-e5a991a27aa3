import { Antecedent<PERSON><PERSON>ess<PERSON> } from '@/domain/models'
import { IGetAllAntecedentGrossesseRepository } from '@/domain/interfaces/repositories/antecedentGrossesse'

export class GetAllAntecedentGrossesseUseCase {
  constructor(
    private readonly getAllAntecedentGrossesseRepository: IGetAllAntecedentGrossesseRepository
  ) {}

  async execute(carnetId: number): Promise<AntecedentGrossesse[]> {
    return await this.getAllAntecedentGrossesseRepository.execute(carnetId)
  }
}
