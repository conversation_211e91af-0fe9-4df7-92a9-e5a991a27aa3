import { Antecedent<PERSON><PERSON>esse } from '@/domain/models'
import { IGetByIdAntecedentGrossesseRepository } from '@/domain/interfaces/repositories/antecedentGrossesse'

export class GetByIdAntecedentGrossesseUseCase {
  constructor(
    private readonly getByIdAntecedentGrossesseRepository: IGetByIdAntecedentGrossesseRepository
  ) {}

  async execute(id: number): Promise<AntecedentGrossesse | null> {
    return await this.getByIdAntecedentGrossesseRepository.execute(id)
  }
}
