import { Antecedent<PERSON>rossesse } from '@/domain/models'
import { IUpdateAntecedentGrossesseRepository } from '@/domain/interfaces/repositories/antecedentGrossesse'

export class UpdateAntecedentGrossesseUseCase {
  constructor(
    private readonly updateAntecedentGrossesseRepository: IUpdateAntecedentGrossesseRepository
  ) {}

  async execute(id: number, data: Partial<AntecedentGrossesse>): Promise<AntecedentGrossesse> {
    const antecedentGrossesse = await this.updateAntecedentGrossesseRepository.execute(id, data)
    return{
      ...antecedentGrossesse,
      date: antecedentGrossesse.date instanceof Date ? new Date(antecedentGrossesse.date).toISOString() : antecedentGrossesse.date,
    }
  }
}
