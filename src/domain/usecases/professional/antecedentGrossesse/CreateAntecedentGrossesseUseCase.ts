import { ICreateAntecedentGrossesseRepository } from '@/domain/interfaces/repositories/antecedentGrossesse'
import { AntecedentGrossesse } from '@/domain/models'

export class CreateAntecedentGrossesseUseCase {
  constructor(
    private readonly createAntecedentGrossesseRepository: ICreateAntecedentGrossesseRepository
  ) {}

  async execute(data: Omit<AntecedentGrossesse, "id">[]): Promise<AntecedentGrossesse[]> {
    const antecedentGrossesse = await this.createAntecedentGrossesseRepository.execute(data)
    const result = antecedentGrossesse.map((r) => {
      return{
        ...r,
        date: r.date instanceof Date ? new Date(r.date).toISOString() : r.date,
      }
    })
    return result
  }
}
