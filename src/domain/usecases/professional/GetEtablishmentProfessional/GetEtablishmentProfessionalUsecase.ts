import { IGetEtablissementProfessionnelRepository } from "@/domain/interfaces/repositories/EtablissementProfessionnel/IGetEtablisementProfessionnelRepository";
import { IGetEtablissementProfessionnelUsecase } from "@/domain/interfaces/usecases/IGetEtablishmentProfessionalUsecase";

class GetEtablishmentProfessionalUsecase implements IGetEtablissementProfessionnelUsecase {
  constructor(
    private readonly getEtablissementProfessionnelRepository: IGetEtablissementProfessionnelRepository
  ) { }

  async execute() {
    try {
      const etablissements = await this.getEtablissementProfessionnelRepository.execute()
      return etablissements
    } catch (error) {
      console.log('get etablishment professionnal error:', error);
    }
  }
}

export default GetEtablishmentProfessionalUsecase