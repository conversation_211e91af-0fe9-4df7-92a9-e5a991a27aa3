import { ConditionGynecologique } from '@/domain/models'
import { IUpdateConditionGynecologiqueRepository } from '@/domain/interfaces/repositories/conditionGynecologique'

export class UpdateConditionGynecologiqueUseCase {
  constructor(
    private readonly updateConditionGynecologiqueRepository: IUpdateConditionGynecologiqueRepository
  ) {}

  async execute(id: number, data: Partial<ConditionGynecologique>): Promise<ConditionGynecologique> {
    const conditionGynecologique = await this.updateConditionGynecologiqueRepository.execute(id, data)
    return{
      ...conditionGynecologique,
      date: conditionGynecologique.date instanceof Date ? new Date(conditionGynecologique.date).toISOString() : conditionGynecologique.date,
    }
  }
}
