import { ConditionGynecologique } from '@/domain/models'
import { IGetByIdConditionGynecologiqueRepository } from '@/domain/interfaces/repositories/conditionGynecologique'

export class GetByIdConditionGynecologiqueUseCase {
  constructor(
    private readonly getByIdConditionGynecologiqueRepository: IGetByIdConditionGynecologiqueRepository
  ) {}

  async execute(id: number): Promise<ConditionGynecologique | null> {
    return await this.getByIdConditionGynecologiqueRepository.execute(id)
  }
}
