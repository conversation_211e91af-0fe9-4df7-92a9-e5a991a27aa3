import { IDeleteConditionGynecologiqueRepository } from "@/domain/interfaces/repositories/conditionGynecologique";

export class DeleteConditionGynecologiqueUseCase {
  constructor(
    private readonly deleteConditionGynecologiqueRepository: IDeleteConditionGynecologiqueRepository
  ) {}

  async execute(id: number): Promise<void> {
    await this.deleteConditionGynecologiqueRepository.execute(id)
  }
}
