import { ICreateConditionGynecologiqueRepository } from '@/domain/interfaces/repositories/conditionGynecologique'
import { ConditionGynecologique } from '@/domain/models'

export class CreateConditionGynecologiqueUseCase {
  constructor(
    private readonly createConditionGynecologiqueRepository: ICreateConditionGynecologiqueRepository
  ) {}

  async execute(data: Omit<ConditionGynecologique, "id">[]): Promise<ConditionGynecologique[]> {
    const conditionGynecologique = await this.createConditionGynecologiqueRepository.execute(data)
    const result = conditionGynecologique.map((r) => {
      return{
        ...r,
        date: r.date instanceof Date ? new Date(r.date).toISOString() : r.date,
      }
    })
    return result
  }
}
