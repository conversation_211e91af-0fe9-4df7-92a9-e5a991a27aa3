import { ConditionGynecologique } from '@/domain/models'
import { IGetAllConditionGynecologiqueRepository } from '@/domain/interfaces/repositories/conditionGynecologique'

export class GetAllConditionGynecologiqueUseCase {
  constructor(
    private readonly getAllConditionGynecologiqueRepository: IGetAllConditionGynecologiqueRepository
  ) {}

  async execute(carnetId: number): Promise<ConditionGynecologique[]> {
    return await this.getAllConditionGynecologiqueRepository.execute(carnetId)
  }
}
