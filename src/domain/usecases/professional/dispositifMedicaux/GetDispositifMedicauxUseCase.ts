import { DispositifMedicaux } from '@/domain/models'
import { IGetDispositifMedicauxRepository } from '@/domain/interfaces/repositories/dispositifMedicaux'

export class GetDispositifMedicauxUseCase {
  constructor(
    private readonly getDispositifMedicauxRepository: IGetDispositifMedicauxRepository
  ) {}

  async getById(id: number): Promise<DispositifMedicaux | null> {
    return await this.getDispositifMedicauxRepository.getById(id)
  }

  async getAll(carnetId: number): Promise<DispositifMedicaux[]> {
    return await this.getDispositifMedicauxRepository.getAll(carnetId)
  }
}
