import { DispositifMedicaux } from '@/domain/models'
import { IUpdateDispositifMedicauxRepository } from '@/domain/interfaces/repositories/dispositifMedicaux'

export class UpdateDispositifMedicauxUseCase {
  constructor(
    private readonly updateDispositifMedicauxRepository: IUpdateDispositifMedicauxRepository
  ) {}

  async execute(id: number, data: Partial<DispositifMedicaux>): Promise<DispositifMedicaux> {
    const dispositifMedicaux = await this.updateDispositifMedicauxRepository.update(id, data)
    return{
      ...dispositifMedicaux,
      date_acquisition: dispositifMedicaux.date_acquisition instanceof Date ? new Date(dispositifMedicaux.date_acquisition).toISOString() : dispositifMedicaux.date_acquisition,
      prochaine_mise_a_jour: dispositifMedicaux.prochaine_mise_a_jour instanceof Date ? new Date(dispositifMedicaux.prochaine_mise_a_jour).toISOString() : dispositifMedicaux.prochaine_mise_a_jour,
    }
  }
}
