import { DispositifMedicaux } from '@/domain/models'
import { ICreateDispositifMedicauxRepository } from '@/domain/interfaces/repositories/dispositifMedicaux'

export class CreateDispositifMedicauxUseCase {
  constructor(
    private readonly createDispositifMedicauxRepository: ICreateDispositifMedicauxRepository
  ) {}

  async execute(data: Omit<DispositifMedicaux, "id">[]): Promise<DispositifMedicaux[]> {
    const dispositifMedicaux = await this.createDispositifMedicauxRepository.create(data)
    const result = dispositifMedicaux.map((r) => {
      return{
        ...r,
        date_acquisition: r.date_acquisition instanceof Date ? new Date(r.date_acquisition).toISOString() : r.date_acquisition,
        prochaine_mise_a_jour: r.prochaine_mise_a_jour instanceof Date ? new Date(r.prochaine_mise_a_jour).toISOString() : r.prochaine_mise_a_jour,
      }
    })
    return result
  }
}
