export enum EmptyStateType {
  AFTER_MAX_DATE = "AFTER_MAX_DATE",
  NO_SLOTS = "NO_SLOTS",
  HAS_NEXT_SLOT = "HAS_NEXT_SLOT",
}

export interface EmptyStateMessage {
  title: string;
  description?: string;
  actionLabel?: string;
}

export const EMPTY_STATE_MESSAGES: Record<EmptyStateType, EmptyStateMessage> = {
  [EmptyStateType.AFTER_MAX_DATE]: {
    title: "Ces dates ne sont pas encore ouvertes à la réservation.",
  },
  [EmptyStateType.NO_SLOTS]: {
    title: "Pas de créneau horaire disponible pour le moment",
  },
  [EmptyStateType.HAS_NEXT_SLOT]: {
    title: " ",
  },
};
