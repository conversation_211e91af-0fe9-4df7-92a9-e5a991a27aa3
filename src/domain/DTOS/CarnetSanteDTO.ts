import { AffectationMedical, Allergie, Antecedant_sociaux, antecedant_sociaux_alcoolique, antecedant_sociaux_fumeur, AntecedantChirurgicaux, AntecedantFamilliaux, AntecedentGrossesse, ConditionGynecologique, DispositifMedicaux, laboratoire_diagnostics, Medicament, Vaccination } from "../models"

export interface CarnetSanteDTO {
  id: number
  id_patient: number
  allergie: Allergie[]
  medicament: Medicament[]
  antecedant_chirurgicaux: AntecedantChirurgicaux[]
  antecedant_familliaux: AntecedantFamilliaux[]
  antecedant_sociaux: Antecedant_sociaux[]
  antecedant_sociaux_alcoolique: antecedant_sociaux_alcoolique[]
  affectation_medical: AffectationMedical[]
  antecedant_sociaux_fumeur: antecedant_sociaux_fumeur[]
  dispositif_medicaux: DispositifMedicaux[]
  vaccination: Vaccination[]
  condition_gynecologique: ConditionGynecologique[]
  antecedent_grossesse: Antecedent<PERSON><PERSON>esse[]
  diagnostic: laboratoire_diagnostics[]
}
