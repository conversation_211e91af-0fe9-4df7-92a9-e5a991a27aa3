import { rendez_vous_statut_enum } from "../models/enums";

export interface AppointmentPatientDTO {
    id: number;
    date_rendez_vous: string;
    time: string;
    professional: {
      id: number;
      nom: string;
      prenom: string;
      titre: string;
      raison_sociale: string;
      adresse: string;
      fokontany: string;
      commune: string;
      avatar: string;
    };
    accessInfo?: string;
    patient_id: number;
    statut: rendez_vous_statut_enum;
    motif: string;
    raison: string;
  }
  