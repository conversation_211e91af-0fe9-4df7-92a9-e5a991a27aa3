import {
  horaire_date_specifique,
  horaire_hebdomadaire,
  pause,
} from "../models";

export interface AvailabilitySettingsDTO {
  id?: number;
  id_professionnel: number;
  type: "hebdomadaire" | "specifique";
  date_debut: Date | string | null;
  date_fin: Date | string | null;
  duree_pause?: number;
  max_rdv_par_jours?: number;
  peut_inviter_autre: boolean;
  temps_moyen_consulation: number;

  horaire_date_specifique?: horaire_date_specifique[];
  horaire_hebdomadaire?: horaire_hebdomadaire[];
  pauses?: pause[];
}

