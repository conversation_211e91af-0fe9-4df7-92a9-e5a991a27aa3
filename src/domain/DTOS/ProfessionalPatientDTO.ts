import { Patient, Urgence } from "../models";

export type CreateProfessionalPatientDTO = Omit<
  Patient,
  "id" | "utilisateur_id" | "unique_id"
> & {
  id_professionnel: number;
};

export interface ProfessionnelPatientDTO {
  id: number;
  id_patient: number;
  id_professionnel: number;
  is_delete: boolean;
  created_date: Date;
  patient: Patient & {
    avatar?: string;
    urgence?: Urgence[];
  };
}
