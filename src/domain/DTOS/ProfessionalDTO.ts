import {
  Contact,
  EtablissementProfessionnel,
  Evenement,
  horaire_hebdomadaire,
  MotClesProfessionnel,
  Professionnel,
  RendezVous,
  SpecialiteProfessionnel,
} from "../models";
import { AvailabilitySettingsDTO } from "./AvailabililtySettingsDTO";

export type SearchProfessionalDTO = Professionnel & {
  rendez_vous: RendezVous[];
  specialites_professionnel: SpecialiteProfessionnel[];
  evenement: Evenement[];
  etablissements_professionnel: EtablissementProfessionnel[];
  parametre_disponibilite: AvailabilitySettingsDTO[];
  contacts: Contact[];
  mot_cles: MotClesProfessionnel[];
};

export type TimeSlotProffessionalCard = {
  date: string;
  start: string;
  end: string;
};

export type ProfessionalCardDTO = Professionnel & {
  specialite: SpecialiteProfessionnel[];
  disponibilite: TimeSlotProffessionalCard[];
  etablissements_professionnel: EtablissementProfessionnel[];
  horaire_hebdomadaire?: horaire_hebdomadaire[];
  contacts: Contact[];
};

export type ProfessionalCompleteDTO = Professionnel & {
  specialites_professionnel: SpecialiteProfessionnel[];
};

export type ProfessionalProfileData = Professionnel & {
  specialites_professionnel: SpecialiteProfessionnel[];
  parametre_disponibilite: AvailabilitySettingsDTO;
  etablissements_professionnel: EtablissementProfessionnel[];
};
