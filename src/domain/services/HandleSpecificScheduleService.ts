import { IAddSpecificDateScheduleRepository } from "@/domain/interfaces/repositories/specificDataSchedule";
import { ICreateTimeSlotRepository } from "@/domain/interfaces/repositories/timelot";
import { IHandleSpecificSchedule } from "@/domain/interfaces/services/IHandleSpecificSchedule";
import { horaire_date_specifique } from "@/domain/models";

export class HandleSpecificScheduleService implements IHandleSpecificSchedule {
    constructor(
        private readonly addSpecificDateScheduleRepository: IAddSpecificDateScheduleRepository,
        private readonly createTimeSlotRepository: ICreateTimeSlotRepository,
    ) { }
    async execute(horaire_date_specifique: horaire_date_specifique[], settingsId: number): Promise<void> {
        const specificDateSchedules = horaire_date_specifique.map(specifique => ({
            id_parametre_disponibilite: settingsId,
            est_specifique: true,
            date: specifique.date
        }));

        const specificDates = await this.addSpecificDateScheduleRepository.execute(specificDateSchedules);

        const specificTimeSlotsPromises = horaire_date_specifique.map(async (specifique, index) => {
            if (specifique.creneau_horaire?.length > 0 && specificDates[index]) {
                const timeSlots = specifique.creneau_horaire.map(creneau => ({
                    id_horaire_specifique: specificDates[index].id,
                    heure_debut: creneau.heure_debut,
                    heure_fin: creneau.heure_fin
                }));
                await this.createTimeSlotRepository.execute(timeSlots);
            }
        });

        await Promise.all(specificTimeSlotsPromises);
    }
}