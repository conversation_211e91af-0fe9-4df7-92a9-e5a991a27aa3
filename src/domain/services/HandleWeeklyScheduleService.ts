import { ICreateTimeSlotRepository } from "@/domain/interfaces/repositories/timelot";
import { IAddWeeklyScheduleRepository } from "@/domain/interfaces/repositories/weeklySchedule";
import { IHandleExceptions } from "@/domain/interfaces/services/IHandleExceptions";
import { IHandleWeeklySchedule } from "@/domain/interfaces/services/IHandleWeeklySchedule";
import { horaire_date_specifique, horaire_hebdomadaire } from "@/domain/models";

export class HandleWeeklyScheduleService implements IHandleWeeklySchedule {
    constructor(
        private readonly addWeeklyScheduleRepository: IAddWeeklyScheduleRepository,
        private readonly createTimeSlotRepository: ICreateTimeSlotRepository,
        private readonly handleExceptionsService: IHandleExceptions,
    ) { }
    async execute(data: {
        horaire_hebdomadaire: horaire_hebdomadaire[];
        horaire_date_specifique?: horaire_date_specifique[];
    }, settingsId: number): Promise<void> {
        // Create weekly schedules
        const horaire_hebdomadaire = data.horaire_hebdomadaire.map(jour => ({
            id_parametre_disponibilite: settingsId,
            jour: jour.jour
        }));
        const weeklySchedules = await this.addWeeklyScheduleRepository.execute(horaire_hebdomadaire);

        // Create time slots for each day in parallel
        const timeSlotsPromises = weeklySchedules.map(async (schedule, index) => {
            const jour = data.horaire_hebdomadaire[index];
            if (jour.creneau_horaire?.length > 0) {
                const timeSlots = jour.creneau_horaire.map(creneau => ({
                    id_horaire_hebdomadaire: schedule.id,
                    heure_debut: creneau.heure_debut,
                    heure_fin: creneau.heure_fin
                }));
                await this.createTimeSlotRepository.execute(timeSlots);
            }
        });

        // Handle exceptions if they exist
        if (data.horaire_date_specifique?.length > 0) {
            const exceptionsPromise = this.handleExceptionsService.execute(data.horaire_date_specifique, settingsId);
            timeSlotsPromises.push(exceptionsPromise);
        }

        await Promise.all(timeSlotsPromises);
    }
}