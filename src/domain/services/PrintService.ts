import { CarnetSanteDTO, ProfessionalCardDTO } from "../DTOS";
import { consultation_medical, Patient, signe_vitaux } from "../models";
import { printCarnetSanteContent } from "./printCarnetSanteContent";
import { printConsultationContent } from "./printConsultationContent";
import { printSignesVitauxContent } from "./printSignesVitauxContent";

export class PrintService {
  /**
   * Helper method to handle the printing process
   * @param contentGenerator Function that generates the HTML content to print
   * @returns Promise that resolves when printing is complete
   */
  private static printContent(contentGenerator: () => string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      try {
        // Create the HTML content first
        const content = contentGenerator();

        // Create a hidden iframe for printing
        const iframe = document.createElement("iframe");
        iframe.style.display = "none";
        document.body.appendChild(iframe);

        if (!iframe.contentWindow) {
          document.body.removeChild(iframe);
          reject(new Error("Failed to create iframe"));
          return;
        }

        // Set up the load handler before writing content
        iframe.onload = () => {
          try {
            // Small delay to ensure content is fully rendered
            setTimeout(() => {
              try {
                iframe.contentWindow?.print();
                // Remove the iframe after a delay to ensure print dialog is shown
                setTimeout(() => {
                  document.body.removeChild(iframe);
                  resolve();
                }, 1000);
              } catch (printError) {
                console.error("Print error:", printError);
                document.body.removeChild(iframe);
                reject(printError);
              }
            }, 100);
          } catch (loadError) {
            console.error("iframe load error:", loadError);
            document.body.removeChild(iframe);
            reject(loadError);
          }
        };

        // Write the content to the iframe
        try {
          iframe.contentWindow.document.open();
          iframe.contentWindow.document.write(content);
          iframe.contentWindow.document.close();
        } catch (writeError) {
          console.error("Content write error:", writeError);
          document.body.removeChild(iframe);
          reject(writeError);
        }
      } catch (error) {
        console.error("General print error:", error);
        reject(error);
      }
    });
  }
  static printCarnetSante(
    carnet: {
      title: string;
      content: {
        id: number;
        text: string;
      }[];
    }[],
    patient: Patient,
    professionnel: ProfessionalCardDTO
  ) {
    return this.printContent(() =>
      printCarnetSanteContent(carnet, patient, professionnel)
    );
  }

  static printConsultation({
    carnet,
    signeVitaux,
    consultations,
    patient,
    professionnel,
  }: {
    carnet: Partial<CarnetSanteDTO>;
    signeVitaux: signe_vitaux;
    consultations: consultation_medical[];
    patient: Patient;
    professionnel: ProfessionalCardDTO;
  }) {
    return this.printContent(() =>
      printConsultationContent({
        carnet,
        signeVitaux,
        consultations,
        patient,
        professionnel,
      })
    );
  }

  static printSignesVitaux(
    signeVitaux: Omit<signe_vitaux, "id">,
    patient: Patient,
    professionnel: ProfessionalCardDTO
  ) {
    return this.printContent(() =>
      printSignesVitauxContent(signeVitaux, patient, professionnel)
    );
  }
}
