import { Evenement } from "@/domain/models";
import {
  addDays,
  eachDayOfInterval,
  eachMonthOfInterval,
  eachWeekOfInterval,
  eachYearOfInterval,
  getDay,
} from "date-fns";
import { IGenerateRecurringEvents } from "../interfaces/services/IGenerateRecurringEvents";

export class GenerateRecurringEvents implements IGenerateRecurringEvents {
  generate(
    event: Evenement,
    viewStart: Date,
    viewEnd: Date,
  ): Omit<Evenement, "id">[] {
    const startDate = new Date(event.date_debut);
    const endDate = new Date(event.date_fin);
    const events: Omit<Evenement, "id">[] = [];

    // Obtenir le jour de la semaine de l'événement (0 = dimanche, 1 = lundi, etc.)
    const eventWeekDay = getDay(startDate);

    // Calculer la durée de l'événement en millisecondes
    const eventDuration = endDate.getTime() - startDate.getTime();

    const dayOfMonth = startDate.getDate(); // Récupérer le jour du mois de la date de début

    switch (event.repetition) {
      case "once":
        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          events.push(event);
        }
        break;

      case "allDays":
        eachDayOfInterval({ start: viewStart, end: viewEnd }).forEach(
          (date) => {
            const recurringStart = new Date(date);
            recurringStart.setHours(
              startDate.getHours(),
              startDate.getMinutes(),
            );
            const recurringEnd = new Date(
              recurringStart.getTime() + eventDuration,
            );

            events.push({
              id_professionnel: event.id_professionnel,
              titre: event.titre || "Rendez-vous",
              est_toute_la_journee: event.est_toute_la_journee,
              description: event.description,
              date_debut: recurringStart,
              date_fin: recurringEnd,
              repetition: event.repetition,
              est_reportee: event.est_reportee,
            });
          },
        );
        break;

      case "allWeeks":
        eachWeekOfInterval(
          { start: viewStart, end: viewEnd },
          { weekStartsOn: eventWeekDay as 0 | 1 | 2 | 3 | 4 | 5 | 6 }, // Commencer la semaine le même jour que l'événement
        ).forEach((date) => {
          const recurringStart = new Date(date);
          recurringStart.setHours(
            startDate.getHours(),
            startDate.getMinutes(),
          );
          const recurringEnd = new Date(
            recurringStart.getTime() + eventDuration,
          );

          events.push({
            id_professionnel: event.id_professionnel,
            titre: event.titre || "Rendez-vous",
            est_toute_la_journee: event.est_toute_la_journee,
            description: event.description,
            date_debut: recurringStart,
            date_fin: recurringEnd,
            repetition: event.repetition,
            est_reportee: event.est_reportee,
          });
        });
        break;

      case "weekly":
        eachWeekOfInterval(
          { start: viewStart, end: viewEnd },
          { weekStartsOn: 1 }, // Commencer la semaine le lundi pour weekly
        ).forEach((weekStart) => {
          // Du lundi au vendredi
          for (let i = 1; i <= 5; i++) { // 1 = lundi, 5 = vendredi
            const date = addDays(weekStart, i - 1);

            // Ne générer des événements qu'à partir de la date de début
            if (date.getTime() >= startDate.getTime()) {
              const recurringStart = new Date(date);
              recurringStart.setHours(
                startDate.getHours(),
                startDate.getMinutes(),
              );
              const recurringEnd = new Date(
                recurringStart.getTime() + eventDuration,
              );

              events.push({
                id_professionnel: event.id_professionnel,
                titre: event.titre || "Rendez-vous",
                est_toute_la_journee: event.est_toute_la_journee,
                description: event.description,
                date_debut: recurringStart,
                date_fin: recurringEnd,
                repetition: event.repetition,
                est_reportee: event.est_reportee,
              });
            }
          }
        });
        break;

      case "allMonths":
        eachMonthOfInterval({ start: viewStart, end: viewEnd }).forEach(
          (date) => {
            const recurringStart = new Date(date);
            recurringStart.setDate(dayOfMonth); // Définir le même jour du mois que la date de début
            recurringStart.setHours(
              startDate.getHours(),
              startDate.getMinutes(),
            );
            const recurringEnd = new Date(
              recurringStart.getTime() + eventDuration,
            );

            // Ne générer l'événement que si la date est valide et après la date de début
            if (
              !isNaN(recurringStart.getTime()) &&
              recurringStart.getTime() >= startDate.getTime()
            ) {
              events.push({
                id_professionnel: event.id_professionnel,
                titre: event.titre || "Rendez-vous",
                est_toute_la_journee: event.est_toute_la_journee,
                description: event.description,
                date_debut: recurringStart,
                date_fin: recurringEnd,
                repetition: event.repetition,
                est_reportee: event.est_reportee,
              });
            }
          },
        );
        break;

      case "allYears":
        eachYearOfInterval({ start: viewStart, end: viewEnd }).forEach(
          (date) => {
            const recurringStart = new Date(date);
            recurringStart.setDate(dayOfMonth); // Définir le même jour du mois que la date de début
            recurringStart.setHours(
              startDate.getHours(),
              startDate.getMinutes(),
            );
            const recurringEnd = new Date(
              recurringStart.getTime() + eventDuration,
            );

            // Ne générer l'événement que si la date est valide et après la date de début
            if (
              !isNaN(recurringStart.getTime()) &&
              recurringStart.getTime() >= startDate.getTime()
            ) {
              events.push({
                id_professionnel: event.id_professionnel,
                titre: event.titre || "Rendez-vous",
                est_toute_la_journee: event.est_toute_la_journee,
                description: event.description,
                date_debut: recurringStart,
                date_fin: recurringEnd,
                repetition: event.repetition,
                est_reportee: event.est_reportee,
              });
            }
          },
        );
        break;
    }

    return events;
  }
}
