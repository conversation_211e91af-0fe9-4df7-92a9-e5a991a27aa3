import { IAddSpecificDateScheduleRepository } from "@/domain/interfaces/repositories/specificDataSchedule";
import { ICreateTimeSlotRepository } from "@/domain/interfaces/repositories/timelot";
import { IHandleExceptions } from "@/domain/interfaces/services/IHandleExceptions";
import { horaire_date_specifique } from "@/domain/models";

export class HandleExceptionsService implements IHandleExceptions {
    constructor(
        private readonly addSpecificDateScheduleRepository: IAddSpecificDateScheduleRepository,
        private readonly createTimeSlotRepository: ICreateTimeSlotRepository,
    ) { }
    async execute(exceptions: horaire_date_specifique[], settingsId: number): Promise<void> {
        const exceptionsDateSchedules = exceptions.map(exception => ({
            id_parametre_disponibilite: settingsId,
            est_specifique: exception.est_specifique,
            date: exception.date
        }));

        const exceptionsData = await this.addSpecificDateScheduleRepository.execute(exceptionsDateSchedules);

        const exceptionTimeSlotsPromises = exceptions.map(async (exception, index) => {
            if (exception.creneau_horaire?.length > 0 && exceptionsData[index]) {
                const timeSlots = exception.creneau_horaire.map(creneau => ({
                    id_horaire_specifique: exceptionsData[index].id,
                    heure_debut: creneau.heure_debut,
                    heure_fin: creneau.heure_fin
                }));
                await this.createTimeSlotRepository.execute(timeSlots);
            }
        });

        await Promise.all(exceptionTimeSlotsPromises);
    }
}