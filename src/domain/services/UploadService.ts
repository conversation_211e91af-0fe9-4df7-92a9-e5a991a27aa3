import { v4 as uuidv4 } from "uuid";
import { IGetPublicUrlRepository, IUploadRepository } from "../interfaces/repositories/uploadFile";
import { IUploadService } from "../interfaces/services/IUploadServices";
import { STORAGE_BUCKET } from "@/infrastructure/repositories/uploadFile/Constant";
import { supabase } from "@/infrastructure/supabase/supabase";
import { upload_file_enum } from "../models/enums";

const DIAGNOSTICS_FOLDER = "images";
const MEDDOC_FOLDER = "diagnostics";
export const MAX_FILE_SIZE = 5 * 1024 * 1024;

export class UploadServices implements IUploadService {
  constructor(
    private readonly uploadRepository: IUploadRepository,
    private readonly getPublicUrlRepository: IGetPublicUrlRepository,
  ) { }

  async execute(file: File, type: upload_file_enum): Promise<string> {
    try {
      // Vérifier si l'utilisateur est authentifié
      const {
        data: { session },
      } = await supabase.auth.getSession();

      console.log(
        "Session status:",
        session ? "Authenticated" : "Not authenticated"
      );

      if (!session) {
        throw new Error("Vous devez être connecté pour uploader une image");
      }

      // Log user info
      console.log("User ID:", session.user.id);
      console.log("User role:", session.user.role);

      // Vérifier si le fichier est une image
      if (!file.type.startsWith("image/")) {
        throw new Error("Le fichier doit être une image");
      }

      // Vérifier la taille du fichier (max 5MB)
      if (file.size > MAX_FILE_SIZE) {
        throw new Error("L'image ne doit pas dépasser 5MB");
      }

      // Sélectionner le dossier approprié
      let folder: string;
      switch (type) {
        case upload_file_enum.diagnostics:
          folder = DIAGNOSTICS_FOLDER;
          break;
        default:
          folder = MEDDOC_FOLDER;
      }

      // Créer un nom de fichier unique avec le chemin du dossier
      const fileExt = file.name.split(".").pop()?.toLowerCase() || "jpg";
      const fileName = `${folder}/${uuidv4()}.${fileExt}`;

      console.log("Attempting to upload file:", fileName);
      console.log("To bucket:", STORAGE_BUCKET);

      // Upload du fichier
      await this.uploadRepository.execute(fileName, file);

      // Récupérer l'URL publique du fichier
      const publicUrl = await this.getPublicUrlRepository.execute(fileName);

      return publicUrl;
    } catch (error) {
      console.error("Upload error:", error);
      throw error;
    }
  }
}