import { CarnetSanteDTO, ProfessionalCardDTO } from "../DTOS";
import { Patient, consultation_medical, signe_vitaux } from "../models";
import logoMEDDoc from "@/assets/logoMEDDoc.png";

export const printConsultationContent = ({
  carnet,
  signeVitaux,
  consultations,
  patient,
  professionnel,
}: {
  carnet: Partial<CarnetSanteDTO>;
  signeVitaux: signe_vitaux;
  consultations: consultation_medical[];
  patient: Patient;
  professionnel: ProfessionalCardDTO;
}) => {
  // Format date
  const currentDate = new Date().toLocaleDateString("fr-FR");
  const birthDate = new Date(patient.date_naissance).toLocaleDateString(
    "fr-FR"
  );
  const age = Math.floor(
    (new Date().getTime() - new Date(patient.date_naissance).getTime()) /
      (365.25 * 24 * 60 * 60 * 1000)
  );
  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Consultation Médicale</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
          font-size: small;
        }

        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          padding: 20px;
          background-color: #f5f5f5;
        }

        .consultation-card {
          max-width: 1000px;
          margin: 0 auto;
          background-color: white;
          padding: 30px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
          border-radius: 8px;
        }

        .grid-cols-2 {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
        }

        .gap-6 {
          gap: 2rem;
        }

        header {
          text-align: center;
          margin-bottom: 30px;
          padding-bottom: 20px;
        }

        h1 {
          color: #2c3e50;
          font-size: 24px;
          margin-bottom: 10px;
        }

        .speciality {
          font-size: 18px;
          color: #34495e;
        }

        .subspeciality, .exam-type {
          font-size: 14px;
          color: #7f8c8d;
        }

        .contact, .order {
          font-size: 13px;
          color: #95a5a6;
        }

        .patient-info {
          background-color: #f8f9fa;
          border-radius: 5px;
        }

        .patient-info p {
          margin-bottom: 5px;
        }

        h2 {
          color: #2c3e50;
          font-size: 18px;
          margin: 20px 0 15px 0;
        }

        ul, ol {
          padding-left: 20px;
        }

        li {
          margin-bottom: 5px;
        }

        .examinations table {
          width: 100%;
          border-collapse: collapse;
          margin: 15px 0;
        }

        .examinations th, .examinations td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: center;
        }

        .examinations th {
          background-color: #f8f9fa;
        }

        .treatment {
          background-color: #f8f9fa;
          border-radius: 5px;
        }

        .treatment ul {
          list-style-type: none;
          padding-left: 0;
        }

        footer {
          display: flex;
          justify-content: space-between;
        }

        .signature {
          font-style: italic;
          color: #7f8c8d;
        }

        .doctor-info {
          color: #34495e;
          line-height: 1.4;
        }

        @media print {
          body {
            background-color: white;
            padding: 0;
          }

          .consultation-card {
            box-shadow: none;
          }
        }
      </style>
    </head>
    <body>
      <div>
        <header>
          <h1>${professionnel?.titre} ${professionnel?.nom} ${professionnel?.prenom}</h1>
          <p class="speciality">${professionnel?.specialite[0].nom_specialite}</p>
          <p class="subspeciality">${professionnel?.specialite
            .map((sp) => sp.nom_specialite)
            .join(", ")}</p>
          <p class="exam-type">${professionnel?.types_consultation}</p>
          <p class="contact">Tél : ${
            professionnel?.contacts &&
            professionnel?.contacts.map((ct) => ct.numero).join(" / ")
          }</p>
          <p class="order">OMI : ${professionnel?.numero_ordre}</p>
        </header>

        <main class="grid-cols-2 gap-6">
          <div>
            <div class="patient-info">
              <p><strong>Date :</strong> ${currentDate}</p>
              <p><strong>NOM et Prénoms :</strong> ${patient.nom} ${patient.prenom}</p>
              <p><strong>Date de Naissance :</strong> ${birthDate} (Age: ${age} ans)</p>
              <p><strong>Tél :</strong> ${patient.telephone || "Non renseigné"}</p>
              <p><strong>Adresse :</strong> ${patient.adresse || "Non renseignée"}</p>
              <p><strong>Motif de consultation :</strong>${consultations[consultations.length - 1]?.raison_de_visite}</p>
            </div>
            <div class="medical-history">
              <h2>Afféctation Médicaux</h2>
              <ul>
                ${
                  carnet.affectation_medical &&
                  carnet.affectation_medical.length > 0
                    ? carnet.affectation_medical
                        .map(
                          (item) => `
                      <li>${item.maladie} ${item.date ? `(depuis ${new Date(item.date).toLocaleDateString("fr-FR")})` : ""} ${item.remarques ? `- ${item.remarques}` : ""}</li>
                    `
                        )
                        .join("")
                    : "<li>Aucun antécédent médical enregistré</li>"
                }
              </ul>
            </div>

            <div class="medical-history">
              <h2>Antécédents Chirurgicaux</h2>
              <ul>
                ${
                  carnet.antecedant_chirurgicaux &&
                  carnet.antecedant_chirurgicaux.length > 0
                    ? carnet.antecedant_chirurgicaux
                        .map(
                          (item) => `
                      <li>${item.nom} ${item.date ? `(${new Date(item.date).toLocaleDateString("fr-FR")})` : ""} ${item.description ? `- ${item.description}` : ""}</li>
                    `
                        )
                        .join("")
                    : "<li>Aucun antécédent chirurgical enregistré</li>"
                }
              </ul>
            </div>

            <div class="risk-factors">
              <h2>Allergies</h2>
              <ul>
                ${
                  carnet.allergie && carnet.allergie.length > 0
                    ? carnet.allergie
                        .map(
                          (item) => `
                      <li><span style="font-weight: bold;">${item.nom}</span> ${item.reaction} ${item.remarques ? `- ${item.remarques}` : ""}</li>
                    `
                        )
                        .join("")
                    : "<li>Aucune allergie enregistrée</li>"
                }
              </ul>
            </div>

            <div class="risk-factors">
              <h2>Facteurs de risque</h2>
              <ul>
                ${
                  carnet.antecedant_sociaux &&
                  carnet.antecedant_sociaux.length > 0
                    ? carnet.antecedant_sociaux
                        .map(
                          (item) => `
                      <li>Consommation: ${item.consommation ? "Oui" : "Non"}, Fréquence: ${item.frequence || "-"}, Quantité: ${item.quantite_consomer || "-"} ${item.remarques ? `- ${item.remarques}` : ""}</li>
                    `
                        )
                        .join("")
                    : ""
                }
                ${
                  carnet.antecedant_sociaux_fumeur &&
                  carnet.antecedant_sociaux_fumeur.length > 0
                    ? carnet.antecedant_sociaux_fumeur
                        .map(
                          (item) => `
                      <li>Tabagisme: ${item.fumeur_actif ? "Actif" : "Non actif"}, ${item.annee_a_fumer ? `Depuis ${item.annee_a_fumer} ans` : ""}, Quantité: ${item.quantite_par_jour || "-"} par jour ${item.remarques ? `- ${item.remarques}` : ""}</li>
                    `
                        )
                        .join("")
                    : ""
                }
                ${
                  carnet.antecedant_sociaux_alcoolique &&
                  carnet.antecedant_sociaux_alcoolique.length > 0
                    ? carnet.antecedant_sociaux_alcoolique
                        .map(
                          (item) => `
                      <li>Alcool: ${item.consommez_activement ? "Consommation active" : "Non actif"}, Fréquence: ${item.frequence || "-"}, Quantité: ${item.nb_boisson_consomer || "-"} ${item.remarques ? `- ${item.remarques}` : ""}</li>
                    `
                        )
                        .join("")
                    : ""
                }
                ${
                  (!carnet.antecedant_sociaux ||
                    carnet.antecedant_sociaux.length === 0) &&
                  (!carnet.antecedant_sociaux_fumeur ||
                    carnet.antecedant_sociaux_fumeur.length === 0) &&
                  (!carnet.antecedant_sociaux_alcoolique ||
                    carnet.antecedant_sociaux_alcoolique.length === 0)
                    ? "<li>Aucun facteur de risque enregistré</li>"
                    : ""
                }
              </ul>
            </div>

            <div class="examinations">
              <h2>Signes Vitaux</h2>
              <table>
                <tr>
                  <th>Date</th>
                  <th>Tension artérielle</th>
                  <th>Fréquence cardiaque</th>
                  <th>Température</th>
                  <th>Poids</th>
                </tr>
                ${
                  signeVitaux &&
                  `
                    <tr>
                      <td>${new Date(signeVitaux.date_visite).toLocaleDateString("fr-FR")}</td>
                      <td>${signeVitaux.tension_arterielle || "-"}</td>
                      <td>${signeVitaux.frequence_cardiaque || "-"}</td>
                      <td>${signeVitaux.temperature || "-"}</td>
                      <td>${signeVitaux.poid || "-"}</td>
                    </tr>
                  `
                }
              </table>
            </div>
          </div>

          <div>
            <div class="family-history">
              <h2>Antécédents Familiaux</h2>
              <ul>
                ${
                  carnet.antecedant_familliaux &&
                  carnet.antecedant_familliaux.length > 0
                    ? carnet.antecedant_familliaux
                        .map(
                          (item) => `
                      <li>${item.nom_lien}: ${item.affections_medicales} ${item.decede ? "(décédé)" : ""} ${item.remarques ? `- ${item.remarques}` : ""}</li>
                    `
                        )
                        .join("")
                    : "<li>Aucun antécédent familial enregistré</li>"
                }
              </ul>
            </div>

            <div class="traitement-suivie">
              <h2>Traitements en cours</h2>
              <ul>
                ${
                  carnet.medicament && carnet.medicament.length > 0
                    ? carnet.medicament
                        .map(
                          (item) => `
                      <li>${item.nom} ${item.quantite_par_dosage ? `${item.quantite_par_dosage} mg` : ""} ${item.calendrier_de_dose ? `: ${item.calendrier_de_dose}` : ""} ${item.remarques ? `- ${item.remarques}` : ""}</li>
                    `
                        )
                        .join("")
                    : "<li>Aucun traitement en cours enregistré</li>"
                }
              </ul>
            </div>

            <div class="conclusion">
              <h2>Dispositifs Médicaux</h2>
              <ul>
                ${
                  carnet.dispositif_medicaux &&
                  carnet.dispositif_medicaux.length > 0
                    ? carnet.dispositif_medicaux
                        .map(
                          (item) => `
                      <li>${item.nom} ${item.marque ? `(${item.marque})` : ""} ${item.modele ? `- Modèle: ${item.modele}` : ""} ${item.remarques ? `- ${item.remarques}` : ""}</li>
                    `
                        )
                        .join("")
                    : "<li>Aucun dispositif médical enregistré</li>"
                }
              </ul>
            </div>

            <div class="treatment">
              <h2>Vaccinations</h2>
              <ul>
                ${
                  carnet.vaccination && carnet.vaccination.length > 0
                    ? carnet.vaccination
                        .map(
                          (item) => `
                      <li>${item.nom_vaccin} ${item.date_administration ? `(${new Date(item.date_administration).toLocaleDateString("fr-FR")})` : ""} ${item.remarques ? `- ${item.remarques}` : ""}</li>
                    `
                        )
                        .join("")
                    : "<li>Aucune vaccination enregistrée</li>"
                }
              </ul>
            </div>

            <div class="treatment">
              <h2>Diagnostics et Tests</h2>
              <ul>
                ${
                  carnet.diagnostic && carnet.diagnostic.length > 0
                    ? carnet.diagnostic
                        .map(
                          (item) => `
                      <li>${item.titre && item.titre} ${item.date ? `(${new Date(item.date).toLocaleDateString("fr-FR")})` : ""} ${item.resultat ? `- Résultat: ${item.resultat}` : ""} ${item.remarque ? `- ${item.remarque}` : ""}</li>
                    `
                        )
                        .join("")
                    : "<li>Aucun diagnostic ou test enregistré</li>"
                }
              </ul>
            </div>
          </div>
        </main>

        <footer>
          <img src="${logoMEDDoc}" style="width: 200px; height: auto;" />
          <div>
            <p class="signature">Je vous prie de croire en mes salutations confraternelles</p>
            <p class="doctor-info">${professionnel?.titre} ${professionnel?.nom} ${professionnel?.prenom}<br>${professionnel?.specialite[0].nom_specialite}<br>OMI: ${professionnel?.numero_ordre}</p>
          </div>
        </footer>
      </div>
    </body>
    </html>
  `;
};
