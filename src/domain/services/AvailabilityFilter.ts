import { Event } from "@/shared/types/SettingsType";
import { AvailabilitySettingsDTO } from "../DTOS";
import { Evenement } from "../models";
import { IDateSplitter } from "../interfaces/services/IDateSplitter";

class AvailabilityFilter {

  constructor(
    private readonly dateSplitter: IDateSplitter
  ) { }

  execute({ availabilities, events, consultation_average }: { availabilities: AvailabilitySettingsDTO[], events: Evenement[], consultation_average: number }) {
    if (!availabilities) return [];

    const event_dates = events.map(event => ({
      start: event.date_debut,
      end: event.date_fin
    }))

    const hebdo_settings = availabilities.filter(availability => availability.type === 'hebdomadaire')
    const spec_settings = availabilities.filter(availability => availability.type === 'specifique')

    const hebdo_dates = hebdo_settings.map(availability => {
      const data = availability.horaire_hebdomadaire.map(day => {
        const d = day.creneau_horaire.map(slot => ({
          start: slot.heure_debut,
          end: slot.heure_fin
        }))
      })
    })

    const spec_dates = spec_settings.map(availability => {
      const data = availability.horaire_date_specifique.map(day => {
        const d = day.creneau_horaire.map(slot => ({
          start: slot.heure_debut,
          end: slot.heure_fin
        }))
      })
    })

    const pauses = availabilities.map(availability => ({
      start: availability.date_debut,
      end: availability.date_fin
    }))

    const splittedEventDates = event_dates.map(date => {
      if (date.start.getTime() > date.end.getTime()) throw new Error("Date de debut superieur a date fin. Invalide...")

      return this.dateSplitter.splitDate(date.start, date.end, consultation_average)
    })

    return splittedEventDates
  }
}

export default AvailabilityFilter