import { Event } from "@/shared/types/SettingsType";
import { AvailabilitySettingsDTO } from "../DTOS";
import { creneau_horaire, Evenement, RendezVous } from "../models";
import { ILoadEvents } from "../interfaces/services/ILoadEvents";
import { B<PERSON><PERSON>K, EVENT_COLOR, GRE<PERSON>, PRIMARY } from "@/shared/constants/Color";
import { AppointmentProfessionalDTO } from "../DTOS/AppointmentProfessionalDTO";

class LoadEvents implements ILoadEvents {
  execute(
    data: {
      disponibilites: AvailabilitySettingsDTO;
      evenement: Evenement[];
      appointments: RendezVous[] | AppointmentProfessionalDTO[];
    },
    filter: {
      isDisponibilites?: boolean;
      isEvenement?: boolean;
      isAppointments?: boolean;
    } = {},
  ) {
    const newEvents: Event[] = [];
    const {
      isDisponibilites = true,
      isEvenement = true,
      isAppointments = true,
    } = filter;

    // Handle appointments
    if (data.appointments && data.appointments.length > 0 && isAppointments) {
      data.appointments.forEach((appointment) => {
        const startDate = new Date(appointment.date_rendez_vous);
        const temps_moyen_consulation =
          data.disponibilites?.temps_moyen_consulation || 30;
        const endDate = new Date(
          startDate.getTime() + temps_moyen_consulation * 60 * 1000,
        );

        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          newEvents.push({
            id: appointment.id,
            title: appointment.patient ? appointment.patient.nom : "",
            type: appointment.statut,
            start: startDate,
            end: endDate,
            allDay: false,
            backgroundColor: GREEN, // Different color for appointments
          });
        }
      });
    }
    // Handle events
    if (data.evenement && data.evenement.length > 0 && isEvenement) {
      data.evenement.forEach((event: Evenement) => {
        const startDate = new Date(event.date_debut);
        const endDate = new Date(event.date_fin);

        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          newEvents.push({
            id: event.id,
            title: event.titre,
            type: "evenement",
            start: startDate,
            end: endDate,
            allDay: false,
            backgroundColor: EVENT_COLOR, // Different color for events
            color: BLACK,
          });
        }
      });
    }

    // Handle availabilities
    if (data.disponibilites && isDisponibilites) {
      if (
        data.disponibilites.type === "specifique" &&
        data.disponibilites.horaire_date_specifique
      ) {
        // Pour les disponibilités uniques
        data.disponibilites.horaire_date_specifique.forEach((jour) => {
          if (jour.creneau_horaire && jour.creneau_horaire.length > 0) {
            jour.creneau_horaire.forEach((slot) => {
              const date = new Date(jour.date);
              if (isNaN(date.getTime())) {
                console.error("Invalid date:", jour.date);
                return;
              }

              const startDate = new Date(date);
              const endDate = new Date(date);

              const [startHours, startMinutes] = slot.heure_debut.split(":");
              const [endHours, endMinutes] = slot.heure_fin.split(":");

              startDate.setHours(
                parseInt(startHours),
                parseInt(startMinutes),
                0,
                0,
              );
              endDate.setHours(parseInt(endHours), parseInt(endMinutes), 0, 0);

              newEvents.push({
                id: slot.id,
                title: "Disponibilite",
                type: "disponibilite",
                start: startDate,
                end: endDate,
                allDay: false,
                backgroundColor: PRIMARY, // Couleur de fond pour les disponibilités
              });
            });
          }
        });
      } else if (
        data.disponibilites.type === "hebdomadaire" &&
        data.disponibilites.horaire_hebdomadaire
      ) {
        // Pour les disponibilités hebdomadaires
        const startDate = data.disponibilites.date_debut
          ? new Date(data.disponibilites.date_debut)
          : new Date();
        let endDate = new Date();

        if (data.disponibilites.date_fin) {
          endDate = new Date(data.disponibilites.date_fin);
        } else {
          endDate.setFullYear(endDate.getFullYear() + 2);
        }
        const currentDate = new Date(startDate);
        while (currentDate <= endDate) {
          const dayOfWeek = currentDate.getDay();
          const daySchedule =
            data.disponibilites.horaire_hebdomadaire[dayOfWeek];

          // Check if there's an exception for the current date
          const currentDateStr = currentDate.toISOString().split("T")[0];
          const hasException = data.disponibilites.horaire_date_specifique
            ?.some(
              (exception) =>
                typeof exception.date === "string" &&
                exception.date.split("T")[0] === currentDateStr,
            );

          if (daySchedule) {
            if (hasException && data.disponibilites.horaire_date_specifique) {
              // Use exception schedule
              const exception = data.disponibilites.horaire_date_specifique
                .find(
                  (exc) =>
                    typeof exc.date === "string" &&
                    exc.date.split("T")[0] === currentDateStr,
                );
              if (exception && exception.creneau_horaire) {
                exception.creneau_horaire.forEach((slot: creneau_horaire) => {
                  const slotStartDate = new Date(currentDate);
                  const slotEndDate = new Date(currentDate);

                  const [startHours, startMinutes] = slot.heure_debut.split(
                    ":",
                  );
                  const [endHours, endMinutes] = slot.heure_fin.split(":");

                  slotStartDate.setHours(
                    parseInt(startHours),
                    parseInt(startMinutes),
                    0,
                    0,
                  );
                  slotEndDate.setHours(
                    parseInt(endHours),
                    parseInt(endMinutes),
                    0,
                    0,
                  );

                  newEvents.push({
                    id: slot.id,
                    title: "Disponibilite",
                    type: "exception",
                    start: slotStartDate,
                    end: slotEndDate,
                    allDay: false,
                    backgroundColor: PRIMARY,
                  });
                });
              }
            } else if (daySchedule.creneau_horaire) {
              // Use regular schedule
              daySchedule.creneau_horaire.forEach((slot: creneau_horaire) => {
                const slotStartDate = new Date(currentDate);
                const slotEndDate = new Date(currentDate);

                const [startHours, startMinutes] = slot.heure_debut.split(":");
                const [endHours, endMinutes] = slot.heure_fin.split(":");

                slotStartDate.setHours(
                  parseInt(startHours),
                  parseInt(startMinutes),
                  0,
                  0,
                );
                slotEndDate.setHours(
                  parseInt(endHours),
                  parseInt(endMinutes),
                  0,
                  0,
                );

                newEvents.push({
                  id: slot.id,
                  title: "Disponibilite",
                  type: "disponibilite",
                  start: slotStartDate,
                  end: slotEndDate,
                  allDay: false,
                  backgroundColor: PRIMARY,
                });
              });
            }
          }
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }
    }

    return newEvents;
  }
}

export default LoadEvents;
