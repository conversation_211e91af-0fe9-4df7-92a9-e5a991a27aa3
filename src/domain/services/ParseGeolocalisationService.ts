class ParseGeolocalisationService {
  constructor() {}

  execute(str: string): { type: "Point"; coordinates: [number, number] } {
    const match = str.match(
      /\(?\s*(-?\d+(\.\d+)?)\s*,\s*(-?\d+(\.\d+)?)\s*\)?/,
    );
    if (!match) throw new Error("Format de géolocalisation invalide");

    const lat = parseFloat(match[1]);
    const lon = parseFloat(match[3]);

    if (lat < -90 || lat > 90) {
      throw new Error(
        `Latitude invalide : ${lat}. Elle doit être entre -90 et 90.`,
      );
    }

    if (lon < -180 || lon > 180) {
      throw new Error(
        `Longitude invalide : ${lon}. Elle doit être entre -180 et 180.`,
      );
    }

    return {
      type: "Point",
      coordinates: [lon, lat], // lon, lat !
    };
  }
}

export default ParseGeolocalisationService;
