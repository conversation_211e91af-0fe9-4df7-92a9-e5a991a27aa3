import { supabase } from "@/infrastructure/supabase/supabase";
import { ISupabaseUploader } from "../interfaces/services/ISupabaseUploader";
import { FilenameGenerator } from "@/domain/services/FilenameGenerator";
import { MAX_FILE_SIZE } from "./UploadService";

export class SupabaseUploader implements ISupabaseUploader {
  constructor(private readonly filenameGenerator: FilenameGenerator) { }

  async uploadFile(
    bucket: string,
    file: File | Blob,
    path: string,
    options?: {
      cacheControl?: string;
      upsert?: boolean;
      contentType?: string;
    },
  ): Promise<{ url?: string; error?: Error }> {

    // Extraire l'extension du fichier à partir du type MIME
    const fileType = file.type || "application/octet-stream";
    const fileExtension = fileType.split("/")[1] || "bin";

    // Générer un nom de fichier unique
    path = `${path ? path + "/" : ""}${this.filenameGenerator.execute(fileExtension)
      }`;

    // Filtrer les fichiers avec la taille de 5Mb max
    if (file.size > MAX_FILE_SIZE) {
      return { error: new Error((`L'image (${(file.size / 1024 / 1024).toFixed(2)}MB) dépasse la limite de 5MB`)) };
    }

    // Upload du fichier vers Supabase
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: options?.cacheControl ?? "3600",
        upsert: options?.upsert ?? false,
        contentType: options?.contentType || fileType,
      });

    if (error) {
      console.error("Erreur lors de l'upload:", error);
      return { error };
    }

    // Récupérer l'URL publique du fichier
    const { publicUrl } = supabase.storage.from(bucket).getPublicUrl(path).data;
    return { url: publicUrl };
  }

  getPublicUrl(bucket: string, path: string): string {
    const { publicUrl } = supabase.storage.from(bucket).getPublicUrl(path).data;
    return publicUrl;
  }
}
