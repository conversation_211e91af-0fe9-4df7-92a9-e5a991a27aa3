import { IUserInformationProvider } from "@/domain/interfaces/services/IUserInformationProvider";
import { PatientInformationProvider } from "@/infrastructure/providers/PatientInformationProvider";
import { ProfessionalInformationProvider } from "@/infrastructure/providers/ProfessionalInformationProvider";
import { AdminInformationProvider } from "@/infrastructure/providers/AdminInformationProvider";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { IProfessionalRepository } from "@/domain/interfaces/repositories";
import { IGetAdminByUserIdRepository } from "@/domain/interfaces/repositories/admins";
import { IGetPatientByUserIdRepository } from "@/domain/interfaces/repositories/patients";

export class UserInformationProviderFactory {
  constructor(
    private readonly patientRepository: IGetPatientByUserIdRepository,
    private readonly professionalRepository: IProfessionalRepository,
    private readonly adminRepository: IGetAdminByUserIdRepository
  ) { }

  createProvider(role: utilisateurs_role_enum): IUserInformationProvider {
    const providers = {
      [utilisateurs_role_enum.PATIENT]: () =>
        new PatientInformationProvider(this.patientRepository),
      [utilisateurs_role_enum.PROFESSIONNEL]: () =>
        new ProfessionalInformationProvider(this.professionalRepository),
      [utilisateurs_role_enum.ADMIN]: () =>
        new AdminInformationProvider(this.adminRepository),
    };

    const providerFactory = providers[role];

    if (!providerFactory) {
      throw new Error(`No provider found for role: ${role}`);
    }

    return providerFactory();
  }
}
