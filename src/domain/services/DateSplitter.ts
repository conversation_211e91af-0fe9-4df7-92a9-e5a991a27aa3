import { TimeSlotProffessionalCard } from "../DTOS";
import { IDateSplitter } from "../interfaces/services/IDateSplitter";

/**
 * Service permettant de découper une plage de temps en créneaux horaires définis.
 */
class DateSplitter implements IDateSplitter {
  /**
   * Divise une plage horaire en intervalles réguliers.
   * @param startDate - Date de début.
   * @param endDate - Date de fin.
   * @param gapMinutes - Durée d'un créneau en minutes.
   * @returns Liste des créneaux sous forme de {date: "YYYY-MM-DD", start: "HH:mm", end: "HH:mm"}.
   */
  splitDate(
    startDate: Date,
    endDate: Date,
    gapMinutes: number,
  ): TimeSlotProffessionalCard[] {
    const result: TimeSlotProffessionalCard[] = [];
    const current = new Date(startDate);
    const gapMs = gapMinutes * 60 * 1000;

    if (startDate.getTime() === endDate.getTime()) {
      current.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
    }

    while (current.getTime() + gapMs <= endDate.getTime()) {
      const nextTime = new Date(current.getTime() + gapMs);

      result.push({
        date: this.formatDate(current),
        start: this.formatTime(current),
        end: this.formatTime(nextTime),
      });

      current.setTime(nextTime.getTime()); // Mise à jour du pointeur temporel
    }

    return result;
  }

  private formatTime(date: Date): string {
    return date.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  private formatDate(date: Date): string {
    return date.toISOString().split("T")[0]; // Format YYYY-MM-DD
  }
}

export default DateSplitter;
