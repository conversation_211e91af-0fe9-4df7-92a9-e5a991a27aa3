import { supabase } from "@/infrastructure/supabase/supabase";
import { IDeleteImageRepository } from "../interfaces/repositories/uploadFile";
import { IDeleteImageService } from "../interfaces/services/IDeleteImageService";

export class DeleteImageService implements IDeleteImageService {
  constructor(
    private readonly deleteImageRepository: IDeleteImageRepository,
  ) { }

  async execute (imageUrl: string): Promise<void> {
    try {
      // Vérifier si l'utilisateur est authentifié
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session) {
        throw new Error("Vous devez être connecté pour supprimer une image");
      }

      // Extraire le nom du fichier de l'URL
      const urlParts = imageUrl.split("/");
      const fileName = `${urlParts[urlParts.length - 2]}/${
        urlParts[urlParts.length - 1]
      }`;

      console.log("Attempting to delete file:", fileName);

      await this.deleteImageRepository.execute(fileName);

      console.log("File deleted successfully");
    } catch (error) {
      console.error("Delete error:", error);
      throw error;
    }
  }
}