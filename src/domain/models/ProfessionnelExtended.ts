import { ListeAssurances } from "./AssuranceProfessionnel.ts";
import { ListeSpecialites } from "./ListeSpecialites.ts";
import { ordre_appartenance } from "./OrdreAppartenance.ts";
import { Professionnel } from "./Professionnel";

/**
 * Extension du modèle Professionnel avec des champs supplémentaires
 * utilisés dans l'application mais non définis dans le modèle de base
 */
export interface ProfessionnelExtended extends Professionnel {
  // Champs supplémentaires pour les assurances et ordres d'appartenance
  assurances_acceptees?: ListeAssurances[];
  ordres_appartenance?: ordre_appartenance[];

  // Champs pour les images
  profile_image?: File | null;
  cabinet_images?: File[] | null;

  // Champ pour les spécialités
  specialites?: ListeSpecialites[] | null;
}
