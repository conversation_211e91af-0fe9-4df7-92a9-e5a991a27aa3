import { action_historique } from './ActionHistorique'

// Noter tout les rendez_vous que ce soit en attente, terminee ou annulee
export interface historiques_medicaux {
  // Schema de la base de donnee
  id: number
  patient_id: number
  id_professionnel: number
  id_action_historique: number
  id_consultation: number
  confidentielite: boolean
  cree_a: number
}

export interface historiques_medicaux_data {
  // Utilisee dans l'application
  id: number
  patient_id: number
  id_professionnel: number
  id_consultation: number
  confidentielite: boolean
  cree_a: number

  // Foreign key
  action_historique: action_historique[]
}
