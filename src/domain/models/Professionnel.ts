import { consultation_medical } from "./ConsultationMedical";
import {
  professionnels_titre_enum,
  professionnels_types_consultation_enum,
  sexe_enum,
} from "./enums";
import { EtablissementProfessionnel } from "./EtablissementProfessionnel";
import { Evenement } from "./Evenement";
import { historiques_medicaux } from "./HistoriqueMedicaux";
import { LangueParleeProfessionnel } from "./LangueParleeProfessionnel";
import { MotClesProfessionnel } from "./MotClesProfessionnel";
import { ordre_appartenance } from "./OrdreAppartenance";
import { parametre_disponibilite } from "./ParametreDisponibilite";
import { Point } from "./Point";
import { RendezVous } from "./RendezVous";
import { SpecialiteProfessionnel } from "./SpecialiteProfessionnel";

export interface Professionnel {
  id: number;
  utilisateur_id: number;
  titre: professionnels_titre_enum;
  nom: string;
  prenom?: string;
  sexe?: sexe_enum;
  numero_ordre: string;
  raison_sociale?: string;
  nif?: string;
  stat?: string;
  presentation_generale?: string;
  temps_moyen_consulation?: number;
  types_consultation: professionnels_types_consultation_enum;
  modes_paiement_acceptes?: string;
  nouveau_patient_acceptes?: boolean;
  adresse: string;
  region?: string;
  district?: string;
  commune?: string;
  fokontany?: string;
  informations_acces: string;
  geolocalisation: string;
}

export interface Professional_data {
  // Utilisee dans l'application
  id: number;
  utilisateur_id: number;
  titre: professionnels_titre_enum;
  nom: string;
  prenom: string;
  sexe?: sexe_enum;
  numero_ordre: string;
  raison_sociale?: string;
  nif?: string;
  stat?: string;
  presentation_generale?: string;
  temps_moyen_consultation?: number;
  types_consultation: professionnels_types_consultation_enum[];
  modes_paiement_acceptes?: string;
  nouveau_patient_acceptes?: boolean;
  contact_email: string;
  reseaux_sociaux?: string;
  geolocalisation?: Point;

  // Relations
  profilePath?: string
  specialites?: SpecialiteProfessionnel[];
  disponibilite?: parametre_disponibilite[];
  rendez_vous?: RendezVous[];
  historiques_medicaux?: historiques_medicaux[];
  consultation_medical?: consultation_medical[];
  etablissements_professionnel?: EtablissementProfessionnel;
  ordre_appartenance?: ordre_appartenance;
  mot_cles?: MotClesProfessionnel[];
  langue_parlees?: LangueParleeProfessionnel[];
}

export interface professionnalAgendaType {
  schedules: parametre_disponibilite[];
  events: Evenement[];
  appointments: RendezVous[];
}

export interface ProfessionalFilters {
  nouveauPatientAcceptes?: boolean;
  sexe?: sexe_enum;
  langueParlee?: string[];
  localisation?: string;
  speciality?: string;
}

export interface ProfessionalSearchParams extends ProfessionalFilters {
  page?: number;
  limit?: number;
}
